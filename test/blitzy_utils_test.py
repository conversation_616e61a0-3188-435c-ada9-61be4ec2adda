#!/usr/bin/env python3
"""
Test script for Azure DevOps pull request and branch handling functions.
This script tests the following functions:
- handle_azure_devops_pull_request
- get_existing_azure_devops_pull_request
- create_azure_devops_pull_request
- _handle_azure_branch_logic

Usage: python main_test_2.py
"""

import os
import sys
import logging
from typing import Optional

# Add the blitzy_utils directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "blitzy_utils"))

# Import Azure DevOps related modules
# Import our custom functions
from blitzy_utils.azure_utils import (
    _handle_azure_branch_logic,
    create_azure_devops_pull_request,
    get_existing_azure_devops_pull_request,
    handle_azure_devops_pull_request,
)
from blitzy_utils.github import _handle_github_branch_logic

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Test configuration - REPLACE THESE WITH YOUR ACTUAL VALUES
TEST_CONFIG = {
    "organization": "blitzy-ai",  # Your Azure DevOps organization
    "project_id": "fddefc6e-6e46-4359-8e98-d01f09ba2a9d",  # Your project ID
    "repository_id": "cec06e35-99ff-4330-a772-470cad144f56",  # Your repository ID
    "access_token": "",  # Your access token
    "github_access_token": "",
    "base_branch": "main",  # Base branch name
    "feature_branch": "feature-branch",  # Feature branch name
    "pr_title": "Test Pull Request",  # PR title
    "pr_body": "This is a test pull request created by the test script.",  # PR description
}


def test_branch_logic():
    """Test branch handling logic."""
    print("\n=== Testing Branch Logic ===")
    try:
        # Test creating a new branch
        print("Testing branch creation...")
        branch_ref = _handle_azure_branch_logic(
            access_token=TEST_CONFIG["access_token"],
            organization=TEST_CONFIG["organization"],
            repo_id=TEST_CONFIG["repository_id"],
            project=TEST_CONFIG["project_id"],
            branch_name="feature-branch-1",
            base_branch="main",
            create_new_branch=True,
            delete_existing_branch=False,
        )
        if branch_ref:
            print(f"✓ Branch created/found: {TEST_CONFIG['feature_branch']}")
            print(f"  - Object ID: {branch_ref.object_id}")
        else:
            print("✗ Branch creation failed")
            return False

        return True

    except Exception as e:
        print(f"✗ Branch logic test failed: {e}")
        return False


def test_get_existing_pull_request():
    """Test getting existing pull request."""
    print("\n=== Testing Get Existing Pull Request ===")
    try:
        existing_pr = get_existing_azure_devops_pull_request(
            organization=TEST_CONFIG["organization"],
            project_id=TEST_CONFIG["project_id"],
            repository_id=TEST_CONFIG["repository_id"],
            access_token=TEST_CONFIG["access_token"],
            head_branch=TEST_CONFIG["feature_branch"],
            base_branch=TEST_CONFIG["base_branch"],
        )
        if existing_pr:
            print("Existing PR", existing_pr)
            return existing_pr
        else:
            print("✓ No existing PR found (this is expected for new branches)")
            return None
    except Exception as e:
        print(f"✗ Get existing PR failed: {e}")
        return None


def test_create_pull_request():
    """Test creating a new pull request."""
    print("\n=== Testing Create Pull Request ===")
    try:
        new_pr = create_azure_devops_pull_request(
            organization=TEST_CONFIG["organization"],
            project_id=TEST_CONFIG["project_id"],
            repository_id=TEST_CONFIG["repository_id"],
            access_token=TEST_CONFIG["access_token"],
            head_branch=f"feature-branch-2",
            base_branch="main",
            pr_title=TEST_CONFIG["pr_title"],
            pr_body=TEST_CONFIG["pr_body"],
        )
        if new_pr:
            print(f"✓ PR created successfully: #{new_pr.pull_request_id}")
            print(f"  - Title: {new_pr.title}")
            print(f"  - Status: {new_pr.status}")
            print(f"  - URL: {new_pr.url}")
            return new_pr
        else:
            print("✗ PR creation failed")
            return None
    except Exception as e:
        print(f"✗ Create PR failed: {e}")
        return None


def test_handle_pull_request():
    """Test the main pull request handler function."""
    print("\n=== Testing Handle Pull Request ===")
    try:
        pr = handle_azure_devops_pull_request(
            organization=TEST_CONFIG["organization"],
            project_id=TEST_CONFIG["project_id"],
            repository_id=TEST_CONFIG["repository_id"],
            access_token=TEST_CONFIG["access_token"],
            head_branch=TEST_CONFIG["feature_branch"],
            base_branch=TEST_CONFIG["base_branch"],
            pr_title=TEST_CONFIG["pr_title"],
            pr_body=TEST_CONFIG["pr_body"],
            repo_project_id=TEST_CONFIG["project_id"],
        )
        if pr:
            print(f"✓ Pull request handled successfully: #{pr.pull_request_id}")
            print(f"  - Title: {pr.title}")
            print(f"  - Status: {pr.status}")
            print(f"  - URL: {pr.url}")
            return pr
        else:
            print("✗ Pull request handling failed")
            return None
    except Exception as e:
        print(f"✗ Handle PR failed: {e}")
        return None


def test_handle_github_branch_logic():
    """Test the GitHub branch logic handler function."""
    print("\n=== Testing Handle GitHub Branch Logic ===")

    try:
        # Setup test repository (adjust based on your test setup)
        from github import Github

        github_client = Github(TEST_CONFIG["github_access_token"])
        repo = github_client.get_repo("chrisb922/Test")

        # Test configuration
        test_branch = "other_2"
        base_branch = "main"

        # Test Case 1: Create new branch when it doesn't exist
        print(f"Test 1: Creating new branch '{test_branch}' from '{base_branch}'")
        branch_ref = _handle_github_branch_logic(
            repo=repo,
            branch_name=test_branch,
            base_branch=base_branch,
            create_new_branch=True,
            delete_existing_branch=False,
        )

    except Exception as e:
        print(f"✗ Branch logic test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def run_all_tests():
    """Run all tests in sequence."""
    print("🚀 Starting Azure DevOps Function Tests")
    print("=" * 50)
    # Test 1: Branch Logic
    if not test_branch_logic():
        print("❌ Branch logic test failed.")
        return

    # Test 2: Get Existing PR
    existing_pr = test_get_existing_pull_request()

    # Test 3: Create PR (only if no existing PR)
    if not existing_pr:
        new_pr = test_create_pull_request()
        if not new_pr:
            print("❌ Create PR test failed.")
            return
    # Test 4: Handle PR
    handled_pr = test_handle_pull_request()
    if not handled_pr:
        print("❌ Handle PR test failed.")
        return

    # Test 5: Handle Branch creation for github
    test_handle_github_branch_logic()

    print("✅ All tests completed successfully!")
    print("📝 Note: Test branch and PR may still exist in your repository.")
    print("   You can manually delete them if needed.")


def print_config():
    """Print the current test configuration."""
    print("Current Test Configuration:")
    print("-" * 30)
    for key, value in TEST_CONFIG.items():
        if key == "access_token":
            # Mask the token for security
            masked_token = (
                value[:10] + "..." + value[-10:] if len(value) > 20 else "***"
            )
            print(f"{key}: {masked_token}")
        else:
            print(f"{key}: {value}")
    print("-" * 30)


if __name__ == "__main__":
    print_config()
    # Ask user to confirm before running tests
    response = input("\nDo you want to run the tests? (y/N): ")
    if response.lower() in ["y", "yes"]:
        run_all_tests()
    else:
        print("Tests cancelled.")
