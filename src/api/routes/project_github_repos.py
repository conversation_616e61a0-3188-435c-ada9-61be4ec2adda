from datetime import datetime
from typing import Any, Dict, List, Optional

from blitzy_utils.consts import DEFAULT_NAME
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (GitHubBranchPattern,
                                  GithubBranchPatternProject,
                                  GitHubProjectRepo, Project,
                                  ProjectInitialType, Status, UsageType)
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response, validate_request
from flask_utils.models_config.model_utils import map_to_model
from sqlalchemy import delete, tuple_
from sqlalchemy.orm import Session

from src.api.models import (GithubRepoOnboardingInput,
                            GithubRepoOnboardingInputList,
                            NewProductGithubRepoOnboardingInput,
                            NewProductGithubRepoOnboardingInputList,
                            ProjectGithubRepoOutput,
                            ProjectGithubRepoOutputList, RepoType, Status200,
                            Status400, Status404, TechSpecJobType,
                            TechSpecPromptInput)
from src.api.routes.job import get_repo_name
from src.api.routes.tech_spec import process_existing_product_job
from src.api.utils.code_gen_utils import validate_code_gen_submit
from src.api.utils.github_installation_utils import (
    create_github_repo_using_github_handler,
    get_github_org_from_github_handler,
    get_github_repo_from_github_handler_with_scv_inference)
from src.api.utils.github_utils import (
    get_branch_head_commit_from_github_handler_with_azure_info,
    get_default_branch_from_github_handler)
from src.consts import REPO_TYPE_PYDANTIC_TO_MODEL_MAPPER
from src.error.errors import (InvalidGithubRepoOnboardError,
                              ProjectOnboardError, Status404Error)
from src.middleware.decorators import get_user_info
from src.service.github_project_repo_service import (
    get_github_project_repo_by_org_repo_branch_company_name,
    get_github_project_repo_by_project_id,
    get_github_project_repo_by_project_id_and_by_github_info)
from src.service.project_service import (get_project_by_id,
                                         get_project_by_user_id_with_relations,
                                         update_project_timeline)
from src.service.tech_spec_service import \
    get_all_tech_specs_by_project_id_order_by_created_at

project_github_bp = Blueprint("project", __name__, url_prefix="")


@project_github_bp.route("/<project_id>/github/repos", methods=["POST"])
@validate_request(GithubRepoOnboardingInputList)
@get_user_info
@flask_pydantic_response
def post_project_github_repos(user_info, project_id, payload: GithubRepoOnboardingInputList):
    project = get_project_by_user_id_with_relations(user_info["id"], project_id)
    if not project:
        logger.warning(f"Project with ID {project_id} not found for user {user_info['id']}.")
        return Status404(message=f"Project with ID {project_id} does not exist"), 404
    logger.info(f"Received github onboarding payload for project {project_id}")
    process_put_project_github_repos(user_info, project, payload)
    return Status200(message="Successfully added Github repos"), 200


@project_github_bp.route("/<project_id>/github/repos", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_project_github_repos(user_info, project_id):
    project = get_project_by_user_id_with_relations(user_info["id"], project_id)
    if not project:
        logger.warning(f"Project with ID {project_id} not found for user {user_info['id']}.")
        return Status404(message=f"Project with ID {project_id} does not exist"), 404

    response_model = fetch_github_repos_by_project_id(project_id)
    return response_model, 200


@project_github_bp.route("/<project_id>/new/product/github/repos", methods=["POST"])
@validate_request(NewProductGithubRepoOnboardingInputList)
@get_user_info
@flask_pydantic_response
def post_add_project_project_github_repos(user_info, project_id, payload: NewProductGithubRepoOnboardingInputList):
    project = get_project_by_user_id_with_relations(user_info["id"], project_id)
    if not project:
        logger.warning(f"Project with ID {project_id} not found for user {user_info['id']}.")
        return Status404(message=f"Project with ID {project_id} does not exist"), 404

    if project.initial_type != ProjectInitialType.NEW_PRODUCT:
        logger.warning(f"Project with ID {project_id} is not a new product project.")
        return Status400(message=f"Project with ID {project_id} is not a new product project"), 400

    logger.info(f"Received github onboarding payload for new product project {project_id}")
    input_payload = payload.input
    validate_new_product_payload_input(user_info, input_payload)
    process_new_product_project_github_repos(user_info, project, input_payload)
    return Status200(message="Successfully added Github repos"), 200


def process_new_product_project_github_repos(user_info, project: Project,
                                             payload: NewProductGithubRepoOnboardingInput):
    user_id = user_info["id"]

    if payload.createRepo:
        logger.info(f"Creating repo for new product project {project.id}")
        repos_by_id = create_github_repo_using_github_handler(
            user_id, payload.orgName,
            payload.repoName, payload.azureProjectId, payload.azureOrgId)
        branch_name = repos_by_id["default_branch"]
        payload.repoId = str(repos_by_id["id"])
        logger.info(f"Created repo for new product project {project.id}")
    else:
        branch_name = get_default_branch_from_github_handler(user_id, payload.orgName, payload.repoId)
        logger.info(f"Fetched default branch for new product project {project.id}")

    logger.info("Generating github project repo models")
    github_onboarding = prepare_github_onboarding_payload(payload, branch_name)

    logger.info("Triggering standard github repo onboarding process.")
    process_github_project_repo_models(user_info, project, github_onboarding)


def process_put_project_github_repos(user_info, project: Project, payload: GithubRepoOnboardingInputList):
    """
    Processes the onboarding of GitHub repositories for a specific project. This
    function ensures that the given payload is validated before insertion into
    the database and logs the progress during these operations.

    :param user_info: Information about the current user initiating the process.
        Typically includes credentials or identification details required
        for validation.
    :param project: The target project instance to which the GitHub repository
        onboarding applies. This is used to determine project-specific
        configuration or attributes.
    :param payload: The input data containing the GitHub repository details
        to be onboarded. The content is validated for correctness based on
        the user's authorization and project requirements.
    """
    project_id = project.id
    logger.info(f"Validated github onboarding payload for project {project_id}")
    logger.info(f"Inserting github onboarding payload for project {project_id}")
    insert_payload_in_the_database(user_info, project, payload)


def validate_new_product_payload_input(user_info: Dict[str, Any], payload: NewProductGithubRepoOnboardingInput) -> None:
    """
    Validates the input payload for a new product's GitHub or Azure repository onboarding process.
    Ensures the organization name and repository ID specified in the payload
    exist within the user's associated GitHub accounts. Raises an error
    if any validation fails.

    :param user_info: Dictionary containing information about the user.
    :param payload: Payload containing details about the new GitHub repository
        onboarding input.
    :return: None
    """
    org_name = payload.orgName
    repo_id = payload.repoId
    azure_org_id = payload.azureOrgId
    azure_project_id = payload.azureProjectId

    has_org_id = azure_org_id is not None and azure_org_id.strip()
    has_project_id = azure_project_id is not None and azure_project_id.strip()

    if has_org_id != has_project_id:
        raise ProjectOnboardError(
            "Invalid Project data: azure_org_id and azure_project_id must both be passed or neither at all."
        )

    elif has_org_id and has_project_id and (azure_org_id == azure_project_id):
        raise ProjectOnboardError(
            "Invalid Project data: azure_org_id and azure_project_id are the same."
        )


    accounts = get_github_org_from_github_handler(user_info["id"])
    if org_name not in accounts:
        raise Status404Error(message=f"Github account {org_name} not found for user {user_info['id']}")

    if not payload.createRepo:
        logger.info(f"Create repo flag is off. Checking if repo {repo_id} exists for org {org_name}")
        repos_by_id = get_github_repo_from_github_handler_with_scv_inference(user_info["id"], org_name, repo_id)

        if repo_id not in repos_by_id:
            raise Status404Error(message=f"Github repo {repo_id} not found for user {user_info['id']}")


def insert_payload_in_the_database(user_info: Dict[str, Any], project: Project, payload: GithubRepoOnboardingInputList):
    """
    Insert GitHub repository payload into the database for the specified project.

    This function generates database models based on the provided user information,
    project, and onboarding payload. It processes these database models to complete
    the insertion operation for onboarding GitHub repositories into the corresponding
    project.

    :param user_info: Dictionary containing details about the user initiating the
        operation.
    :param project: The project entity where the repository data needs to be added.
    :param payload: List of onboarding payloads for GitHub repositories to be processed.
    :return: None
    """
    process_github_project_repo_models(user_info, project, payload)


def generate_github_project_repo_db_payload(user_info: Dict[str, Any], project: Project,
                                            payload: GithubRepoOnboardingInputList,
                                            session: Optional[Session] = None) -> List[Any]:
    """
    Generates a list of database payloads for onboarding GitHub project repositories. This function
    processes each item in the provided payload list, determines the repository type, and calls the
    appropriate model generator based on the repository's type. If the repository type is invalid,
    an error is logged, and an exception is raised.

    :param user_info: Dictionary containing user-specific information required for onboarding.
    :param project: Project instance representing the GitHub project to which the repositories are
        being onboarded.
    :param payload: List containing the input data required to onboard repositories. Each entry
        specifies the type and details of the repository.
    :param session: Client session if any.
    :return: A list of database payloads, where each entry corresponds to the onboarding model of
        a GitHub repository determined by its type.
    """
    with get_db_session(session) as session:
        models_list = []
        for github_input in payload.input:
            onboard_type = github_input.type
            if skip_github_model_generation(project.id, github_input, session):
                logger.info(f"Github repo {github_input.orgName}/{github_input.repoId}/{github_input.branchName}"
                            f" with onboarding type {onboard_type} already exists")
                continue
            if onboard_type == RepoType.SOURCE:
                models_list.append(get_github_project_repo_model_for_input(user_info, project, github_input))
            elif onboard_type == RepoType.TARGET:
                models_list.append(get_github_project_repo_model_for_output(user_info, project, github_input))
            else:
                logger.error(f"Invalid repo onboarding type: {onboard_type}")
                raise ValueError(f"Invalid repo type: {onboard_type}")

        return models_list


def get_github_project_repo_model_for_input(user_info: Dict[str, Any], project: Project,
                                            payload: GithubRepoOnboardingInput):
    """
    Generates a GitHubProjectRepo model based on provided input.

    This function processes the input details including user information, project,
    and payload data to construct a GitHubProjectRepo instance. It retrieves
    necessary details such as repository information, branch commit, and
    organization details, and maps them to a GitHubProjectRepo object for further
    use.

    :param user_info: Contains detailed information about the user, such as
        user id and related attributes. Expected to be a dictionary.
    :param project: An instance of the `Project` class, representing the project
        associated with the repository.
    :param payload: A `GithubRepoOnboardingInput` object including repository and
        branch details necessary for onboarding.
    :return: A `GitHubProjectRepo` object populated with extracted and processed
        data for the input repository.
    """
    accounts = get_github_org_from_github_handler(user_info["id"])
    org_details = accounts.get(payload.orgName)

    repo_dict = get_github_repo_from_github_handler_with_scv_inference(user_info["id"], payload.orgName, payload.repoId)
    repo_details = repo_dict[payload.repoId]

    head_commit_info = get_branch_head_commit_from_github_handler_with_azure_info(user_info["id"], payload.orgName, payload.repoId,
                                                                  payload.branchName, payload.azureOrgId, payload.azureProjectId)
    if not head_commit_info:
        raise InvalidGithubRepoOnboardError(
            message=f"Branch {payload.branchName} not found for repo {payload.repoName}")
    commit_sha = head_commit_info.get("commit", {}).get("sha")

    github_project_repo = GitHubProjectRepo()
    github_project_repo.project_id = project.id
    github_project_repo.usage_type = UsageType.SOURCE

    github_project_repo.org_id = org_details["installationId"]
    github_project_repo.org_name = payload.orgName

    github_project_repo.repo_id = payload.repoId
    github_project_repo.repo_name = repo_details["name"]

    github_project_repo.branch_name = payload.branchName
    github_project_repo.onboarding_commit_hash = commit_sha
    github_project_repo.current_commit_hash = commit_sha
    github_project_repo.previous_commit_hash = commit_sha
    github_project_repo.github_current_commit_hash = commit_sha
    github_project_repo.needs_scan = False
    github_project_repo.last_scan_at = datetime.utcnow()
    github_project_repo.azure_project_id = payload.azureProjectId
    github_project_repo.azure_org_id = payload.azureOrgId
    return github_project_repo


def get_github_project_repo_model_for_output(user_info: Dict[str, Any], project: Project,
                                             payload: GithubRepoOnboardingInput) -> GitHubProjectRepo:
    """
    Generates a `GitHubProjectRepo` model by extracting and organizing GitHub
    repository metadata for use in output data structures. The function takes
    user account information, project details, and payload with repository
    onboarding input, then maps and enriches repository data with relevant
    metadata, organizational details, and usage specifications.

    This method is typically used to set up the required repository information
    for further processing or integrations, ensuring all necessary attributes are
    properly structured and initialized.

    :param user_info: Dictionary with user account information, containing the
        user's unique identifier and additional metadata.
    :param project: Project object representing the ongoing project for which
        the GitHub repository is being processed.
    :param payload: Data payload containing input details for onboarding the
        GitHub repository, including organization name, repository details,
        and configuration preferences.
    :return: A `GitHubProjectRepo` instance with all relevant properties related
        to the given GitHub repository, including its usage type, identifiers,
        metadata, and processing state.
    """
    user_id = user_info["id"]
    accounts = get_github_org_from_github_handler(user_id)
    org_details = accounts.get(payload.orgName)
    if payload.createRepo:
        payload.repoName = payload.repoName or get_repo_name(project.repo_prefix)
        repos_by_id = create_github_repo_using_github_handler(
            user_id, payload.orgName,
            payload.repoName, payload.azureProjectId, payload.azureOrgId)
        repo_id = repos_by_id["id"]
        repo_name = repos_by_id["name"]
    else:
        repo_dict = get_github_repo_from_github_handler_with_scv_inference(user_id, payload.orgName, payload.repoId)
        repo_id = payload.repoId
        repo_name = repo_dict[payload.repoId]["name"]

    github_project_repo = GitHubProjectRepo()
    github_project_repo.project_id = project.id
    github_project_repo.usage_type = UsageType.TARGET

    github_project_repo.org_id = org_details["installationId"]
    github_project_repo.org_name = payload.orgName
    github_project_repo.azure_project_id = payload.azureProjectId
    github_project_repo.azure_org_id = payload.azureOrgId

    github_project_repo.repo_id = repo_id
    github_project_repo.repo_name = repo_name

    github_project_repo.branch_name = payload.branchName
    github_project_repo.create_repo = payload.createRepo
    github_project_repo.needs_scan = False
    github_project_repo.last_scan_at = datetime.utcnow()
    github_project_repo.repo_metadata = {}

    return github_project_repo


def create_branch_pattern_record(user_info: Dict[str, Any], db_model: GitHubProjectRepo, session: Session):
    """
    Creates or links a branch pattern record in the database for a specific GitHub project repository.

    This function checks if a branch pattern for the given organization, repository,
    and branch name already exists in the database. If it does not exist, it creates one.
    Additionally, it ensures the branch pattern is associated with the project.

    :param db_model: The data model object representing the GitHub project repository. This includes information
        such as organization name, repository name, and branch name.
    :param user_info: User information.
    :param session: A SQLAlchemy session object used for database operations.
    :return: None
    """
    org_name = db_model.org_name
    repo_name = db_model.repo_name
    branch_name = db_model.branch_name

    company_id = user_info["company_id"] if user_info.get("company_id") else DEFAULT_NAME
    branch_pattern = get_github_project_repo_by_org_repo_branch_company_name(org_name, repo_name, branch_name,
                                                                             company_id, session)

    if not branch_pattern:
        # Create branch pattern.
        branch_pattern = get_github_branch_pattern_model(db_model, company_id)
        session.add(branch_pattern)
        session.flush()

    # Ultimately link branch pattern with the project.
    branch_pattern_project = get_github_branch_pattern_project_model(db_model, branch_pattern)
    session.add(branch_pattern_project)
    logger.info(f"Inserted branch pattern record for project {db_model.project_id} and branch {branch_name}")


def process_github_project_repo_models(user_info: Dict[str, Any], project: Project,
                                       payload: GithubRepoOnboardingInputList):
    """
    Processes GitHub project repository models by managing branch pattern records and onboarding records in the
    database as per the given project.

    This function handles database operations to delete existing branch pattern records and onboarding records
    associated with the given project. It processes the provided GitHub repository models list, adding each model
    to the database session and creating corresponding branch pattern records if the usage type matches the expected
    criteria. After processing, the changes are committed to the database.

    :param user_info: User info object.
    :param project: Project instance for which the GitHub repository models and branch patterns are to be handled.
    :param payload: Github user input.
    """
    with get_db_session() as session:
        # Delete github onboarding records.
        validate_code_gen_submit(project)
        db_models = generate_github_project_repo_db_payload(user_info, project, payload, session)

        # Delete non-matching branch patterns.
        # We only have one API which handles the project onboarding. We have to delete the records which
        # doesn't match the sent payload.
        delete_non_matching_branch_patterns(project.id, payload, session)

        for db_model in db_models:
            session.add(db_model)

            # Create branch pattern for both source and
            create_branch_pattern_record(user_info, db_model, session)

        _project = get_project_by_id(project.id, session)
        _project.status = Status.GITHUB_COMPLETED

        update_project_timeline(project.id, session)
        session.commit()
        logger.info(f"Inserted {len(db_models)} new github onboarding records for project {project.id}")


def get_github_branch_pattern_model(db_model: GitHubProjectRepo, company_id: str) -> GitHubBranchPattern:
    """
    Transforms a GitHub repository representation from `GitHubProjectRepo` model to
    `GitHubBranchPattern` model. This function extracts the organization name,
    repository name, and branch name from the provided `GitHubProjectRepo` instance,
    and assigns these values to a new instance of `GitHubBranchPattern`. It is useful
    when converting between two distinct data models related to GitHub projects
    and their branch details.

    :param db_model: An instance of `GitHubProjectRepo` containing the details
        of the GitHub project such as organization name, repository name,
        and branch name.
    :param company_id: Company ID.
    :return: An instance of `GitHubBranchPattern` populated with the values
        extracted from the input `GitHubProjectRepo` model.
    """
    github_branch_pattern = GitHubBranchPattern()
    github_branch_pattern.org_name = db_model.org_name
    github_branch_pattern.repo_name = db_model.repo_name
    github_branch_pattern.branch_name = db_model.branch_name
    github_branch_pattern.repo_id = db_model.repo_id
    github_branch_pattern.company_id = company_id

    return github_branch_pattern


def get_github_branch_pattern_project_model(db_model: GitHubProjectRepo,
                                            branch_pattern: GitHubBranchPattern) -> GithubBranchPatternProject:
    """
    This function creates and returns an instance of GithubBranchPatternProject by combining data
    from a GitHubProjectRepo and GitHubBranchPattern. It sets appropriate values for project_id,
    pattern_id, and usage_type based on the provided inputs.

    :param db_model: An instance of GitHubProjectRepo containing project-related information.
    :param branch_pattern: An instance of GitHubBranchPattern containing branch pattern details.
    :return: A new instance of GithubBranchPatternProject with populated attributes.
    :rtype: GithubBranchPatternProject
    """
    branch_pattern_project = GithubBranchPatternProject()
    branch_pattern_project.project_id = db_model.project_id
    branch_pattern_project.pattern_id = branch_pattern.id
    branch_pattern_project.usage_type = db_model.usage_type

    return branch_pattern_project


def skip_github_model_generation(project_id: str, github_input: GithubRepoOnboardingInput, session: Session) -> bool:
    """
    Determines if the GitHub model generation should be skipped for a given project
    and GitHub repository information. This function checks if a matching GitHub
    repository for the specified project with similar attributes (organization
    name, repository ID, branch name, and onboarding type) already exists in the
    database. If found, it indicates that the repository has been already onboarded
    and skips further processing.

    :param project_id: The unique identifier of the project within the system.
    :param github_input: An instance of `GithubRepoOnboardingInput` containing
        information about the GitHub repository such as organization name,
        repository ID, branch name, and type.
    :param session: The database session used to query and interact with the
        repository data.
    :return: A boolean value indicating whether to skip the GitHub model
        generation. Returns True if a matching repository exists, and False
        otherwise.
    """
    onboard_type = REPO_TYPE_PYDANTIC_TO_MODEL_MAPPER[github_input.type]
    github_repo = get_github_project_repo_by_project_id_and_by_github_info(
        project_id,
        github_input.orgName,
        github_input.repoId,
        github_input.branchName,
        onboard_type,
        session
    )

    if github_repo:
        return True

    return False


def fetch_github_repos_by_project_id(project_id: str) -> ProjectGithubRepoOutputList:
    """
    Fetches GitHub repositories associated with a given project ID and maps them to a Pydantic model output.

    This function retrieves the GitHub repositories linked to a specific project ID by querying the database or
    external service. The results are converted to a Pydantic data model format and returned as a list of results
    within a structured output object.

    :param project_id: The unique identifier of the project whose GitHub repositories are to be retrieved.
    :return: A Pydantic model containing the list of GitHub repositories associated with the specified project ID.
    """
    github_repos = get_github_project_repo_by_project_id(project_id)
    project_github_repo_output_list = ProjectGithubRepoOutputList()
    github_repo_list = []
    for github_repo in github_repos:
        pydantic_model = map_to_model(github_repo, ProjectGithubRepoOutput, {"type": "usage_type"})
        github_repo_list.append(pydantic_model)
    project_github_repo_output_list.results = github_repo_list
    return project_github_repo_output_list


def trigger_extend_project_job(user_info: Dict[str, Any], project_info: Project, session: Session = None):
    """
    Trigger extend project job. This function triggers tech spec job only if no tech spec job exists for the project.
    :param user_info: User info.
    :param project_info: Project info.
    :param session: Client session if any.
    """
    # Validate whether any other tech-spec job exists.
    if project_info.initial_type != ProjectInitialType.EXISTING_PRODUCT:
        logger.warning("Project type is not existing product. Skipping tech spec job creation.")
        return

    tech_specs = get_all_tech_specs_by_project_id_order_by_created_at(project_info.id)

    if len(tech_specs) > 0:
        logger.warning(
            f"Tech spec creation job already exists for project {project_info.id}. Skipping tech spec job creation.")
        return

    payload = TechSpecPromptInput(type=TechSpecJobType.EXISTING_PRODUCT)
    process_existing_product_job(user_info, project_info, payload, session)


def delete_non_matching_branch_patterns(project_id: str, payload: GithubRepoOnboardingInputList, session: Session):
    """
    Deletes records from the database that do not match patterns provided in the GithubRepoOnboardingInputList.

    This function ensures cleanup of unrelated branch pattern records based on user-provided onboarding payload.

    :param project_id: Project ID for which records need to be deleted.
    :param payload: List of GitHub repository onboarding input data, specifying valid patterns.
    :param session: The database session used for querying and deletion of records.
    """
    # Extract valid patterns from the payload.
    valid_patterns = {(repo_input.orgName, repo_input.repoId, repo_input.branchName)
                      for repo_input in payload.input}

    if not valid_patterns:
        logger.info("No patterns provided, skipping deletion.")
        return

    # Delete records where the branch pattern does not match any in the valid patterns
    stmt = delete(GitHubProjectRepo).where(
        GitHubProjectRepo.project_id == project_id,
        ~tuple_(
            GitHubProjectRepo.org_name,
            GitHubProjectRepo.repo_id,
            GitHubProjectRepo.branch_name
        ).in_(valid_patterns)
    )
    result = session.execute(stmt)
    logger.info(f"Deleted {result.rowcount} branch pattern records that do not match the given payload patterns.")


def prepare_github_onboarding_payload(payload: NewProductGithubRepoOnboardingInput, branch_name: str):
    org_name = payload.orgName
    repo_id = payload.repoId
    repo_name = payload.repoName
    azure_project_id = payload.azureProjectId
    azure_org_id = payload.azureOrgId

    source_github_onboard = GithubRepoOnboardingInput(
        orgName=org_name,
        repoId=repo_id,
        repoName=repo_name,
        azureProjectId=azure_project_id,
        azureOrgId=azure_org_id,
        branchName=branch_name,
        type=RepoType.SOURCE,
        createRepo=False
    )

    target_github_onboard = GithubRepoOnboardingInput(
        orgName=org_name,
        repoId=repo_id,
        repoName=repo_name,
        azureProjectId=azure_project_id,
        azureOrgId=azure_org_id,
        branchName=branch_name,
        type=RepoType.TARGET,
        createRepo=False
    )
    input_list = [source_github_onboard, target_github_onboard]
    onboarding_payload = GithubRepoOnboardingInputList(input=input_list)
    return onboarding_payload
