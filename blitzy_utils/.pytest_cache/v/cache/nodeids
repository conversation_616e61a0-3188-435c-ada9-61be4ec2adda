["blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsIntegrationScenarios::test_create_all_pull_requests_with_submodules", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsPullRequests::test_create_all_pull_requests_no_submodules", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsPullRequests::test_create_single_pull_request_azure_error", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsPullRequests::test_create_single_pull_request_branch_ref_formatting", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsPullRequests::test_create_single_pull_request_existing_pr", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsPullRequests::test_create_single_pull_request_invalid_parameters", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsPullRequests::test_create_single_pull_request_new_pr", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsUnifiedFunctions::test_create_all_pull_requests_azure_devops_detection", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsUnifiedFunctions::test_create_single_pull_request_azure_devops_detection", "blitzy_utils/test/test_azure_pull_requests.py::TestAzureDevOpsUnifiedFunctions::test_create_single_pull_request_github_fallback", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_branch_reference_formatting", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_concurrent_pull_request_creation", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_create_all_pull_requests_azure_devops", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_create_single_pull_request_azure_devops", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_create_single_pull_request_duplicate_detection", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_error_handling_and_retry", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_large_pr_content", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_pull_request_parameter_validation", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_unified_create_all_pull_requests", "blitzy_utils/test/test_azure_pull_requests_integration.py::test_unified_create_single_pull_request"]