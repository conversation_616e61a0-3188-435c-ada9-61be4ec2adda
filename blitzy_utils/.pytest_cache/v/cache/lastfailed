{"blitzy_utils/test/test_azure_pull_requests_integration.py::test_create_single_pull_request_azure_devops": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_create_single_pull_request_duplicate_detection": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_create_all_pull_requests_azure_devops": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_unified_create_single_pull_request": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_pull_request_parameter_validation": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_branch_reference_formatting": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_error_handling_and_retry": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_large_pr_content": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_concurrent_pull_request_creation": true, "blitzy_utils/test/test_azure_pull_requests_integration.py::test_unified_create_all_pull_requests": true}