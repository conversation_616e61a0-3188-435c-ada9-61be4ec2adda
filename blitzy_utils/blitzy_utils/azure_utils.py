import logging
from typing import List, <PERSON><PERSON>, Dict, <PERSON><PERSON>, Set
from dataclasses import dataclass

from msrest.authentication import BasicAuthentication
import os
import requests

# Handle imports that might fail due to relative import issues
from blitzy_utils.common import blitzy_exponential_retry, get_google_authorized_request_headers, BlitzyGitFile
from blitzy_utils.disk import write_file_to_disk
from blitzy_utils.errors import FailedToFetchCredentials
from azure.devops.connection import Connection
from azure.devops.exceptions import AzureDevOpsServiceError
from azure.devops.v7_1.git.models import (
    GitVersionDescriptor,
    GitRepositoryCreateOptions,
    GitPullRequest,
    GitPullRequestSearchCriteria,
    IdentityRef,
)

logger = logging.getLogger(__name__)

@dataclass
class AzureRepo:
    """Azure DevOps repository info class"""
    org_id: str
    project_id: str
    repo_id: str

@dataclass
class GitProjectRepo:
    """Azure DevOps repository credentials and metadata"""
    access_token: str
    azure_org_id: str
    azure_org_name: str
    azure_project_id: str
    repo_id: str


def _get_azure_devops_credentials_by_repo_id(git_project_repo_id: str) -> GitProjectRepo:
    """Get Azure DevOps credentials by repo id using github_handler service"""
    github_handler_server = os.environ.get('SERVICE_URL_GITHUB')
    if not github_handler_server:
        raise Exception("SERVICE_URL_GITHUB not set, can't fetch credentials by repo id. Please set it.")
    try:
        url = f"{github_handler_server}/v1/github/repositories/{git_project_repo_id}/access-token"
        logger.debug(f"Fetching Azure DevOps credentials from URL: {url}")
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers, timeout=60)
        if response.status_code == 200:
            logger.debug("Successfully fetched Azure DevOps credentials by repo id")
            data = response.json()
            logger.debug(f"Azure DevOps credentials response: {data}")
            return GitProjectRepo(
                access_token=data['access_token'],
                azure_org_id=data['azure_org_id'],
                azure_org_name=data['organization'],
                azure_project_id=data['azure_project_id'],
                repo_id=data['repo_id']
            )
        else:
            err_msg = (f"Failed to fetch Azure DevOps credentials from {url}. "
                       f"Status code: {response.status_code}, response: {response.text}")
            raise FailedToFetchCredentials(err_msg)
    except FailedToFetchCredentials:
        raise  # this is an exception we just raised, no need to process just reraise
    except Exception as e:
        logger.error(f"Error fetching Azure DevOps access token: {e}")
        raise FailedToFetchCredentials(
            f"Failed to fetch Azure DevOps credentials for repo id {git_project_repo_id}, error {e}"
        ) from e


def _create_azure_connection(organization: str, access_token: str):
    """Create an Azure DevOps connection using the SDK."""
    credentials = BasicAuthentication('', access_token)
    organization_url = f"https://dev.azure.com/{organization}"
    
    connection = Connection(
        base_url=organization_url,
        creds=credentials   
    )
    return connection

@blitzy_exponential_retry()
def _create_azure_devops_repo(organization: str, project_id: str, repo_name: str, access_token: str) -> dict:
    """Create a new Azure DevOps repository."""
    if not repo_name or not repo_name.strip():
        raise ValueError("Repository name cannot be empty")

    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()

    try:
        repo_options = GitRepositoryCreateOptions(
            name=repo_name,
            project={"id": project_id}
        )

        created_repo = git_client.create_repository(
            git_repository_to_create=repo_options,
            project=project_id
        )

        logger.info(f"Successfully created Azure DevOps repository '{repo_name}' with ID: {created_repo.id}")

        return {
            "id": created_repo.id,
            "name": created_repo.name,
            "url": getattr(created_repo, 'remote_url', None),
            "web_url": getattr(created_repo, 'web_url', None)
        }

    except AzureDevOpsServiceError as e:
        logger.error(f"Failed to create Azure DevOps repository '{repo_name}': {e}")
        raise


def _get_azure_repo_id_by_name(org_name: str, project_id: str, repo_name: str, access_token: str) \
        -> Optional[str]:
    """Get Azure DevOps repository ID by name if repo exists, otherwise returns None"""
    connection = _create_azure_connection(org_name, access_token)
    git_client = connection.clients.get_git_client()

    try:
        # Get all repositories in the project
        repositories = git_client.get_repositories(project_id)

        # Find the repository by name
        for repository in repositories:
            if repository.name == repo_name:
                logger.debug(f"Found repository '{repo_name}' with ID: {repository.id}")
                return repository.id

        logger.warning(f"Repository '{repo_name}' not found in project '{project_id}'")
        return None

    except AzureDevOpsServiceError as e:
        logger.error(f"Error getting Azure DevOps repositories for project '{project_id}': {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting repository '{repo_name}': {e}")
        raise


def _get_azure_devops_repo(organization: str, project_id: str, repo_id: str, access_token: str) -> Optional[Dict]:
    """Get Azure DevOps repository information using SDK."""
    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()

    try:
        repository = git_client.get_repository(repo_id, project_id)
    except AzureDevOpsServiceError as e:
        if e.type_key == "GitRepositoryNotFoundException":
            logger.warning(f"Repository '{repo_id}' not found in project '{project_id}'")
            return None
        else:
            logger.error(f"Error getting Azure DevOps repository '{repo_id}': {e}")
            raise
    return {
        "id": repository.id,
        "name": repository.name,
        "url": repository.url,
        "default_branch": repository.default_branch
    }

def _get_project_id_by_name(org_name: str, project_name: str, access_token: str) -> Optional[str]:
    """Get Azure DevOps project ID by name if a project exists, otherwise returns None"""
    connection = _create_azure_connection(org_name, access_token)
    core_client = connection.clients.get_core_client()

    try:
        # Get all projects in the organization
        projects = core_client.get_projects()

        # Find the project by name
        for project in projects:
            if project.name == project_name:
                logger.debug(f"Found project '{project_name}' with ID: {project.id}")
                return project.id

        logger.warning(f"Project '{project_name}' not found in organization '{org_name}'")
    except Exception as e:
        logger.error(f"Failed to get project_id for project {project_name}: {e}")
    return None

def _list_azure_devops_items(
        organization: str, project_id: str, repo_id: str, commit_hash: str, access_token: str
) -> List[Dict]:
    """List items in Azure DevOps repository using SDK."""
    connection = _create_azure_connection(organization, access_token)

    git_client = connection.clients.get_git_client()
    
    version_descriptor = GitVersionDescriptor(version=commit_hash, version_type="commit")
    items = git_client.get_items(
        repository_id=repo_id,
        project=project_id,
        recursion_level="Full",
        version_descriptor=version_descriptor
    )
    
    result = []
    for item in items:
        result.append({
            "path": item.path,
            "gitObjectType": "blob" if not getattr(item, 'is_folder', False) else "tree",
            "size": getattr(item, 'size', 0),
            "url": getattr(item, 'url', None)
        })
    
    return result


def _get_azure_devops_file_content(
        organization: str, project_id: str, repo_id: str, path: str, commit_hash: str, access_token: str
) -> Optional[str]:
    """Get file content from Azure DevOps using SDK."""
    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()
    version_descriptor = GitVersionDescriptor(version=commit_hash, version_type="commit")
    
    try:
        file_content_bytes = git_client.get_item_content(
            repository_id=repo_id,
            project=project_id,
            path=path,
            version_descriptor=version_descriptor
        )
        if file_content_bytes is None:
            logger.info(f"File {path} not found in repository")
            return None
        
        # Handle different return types from Azure SDK
        if hasattr(file_content_bytes, 'read'):
            content = file_content_bytes.read()
        elif isinstance(file_content_bytes, (bytes, bytearray)):
            content = file_content_bytes
        else:
            # Assume generator of bytes
            content = b"".join(file_content_bytes)

        # Try to decode as UTF-8
        try:
            return content.decode("utf-8")
        except UnicodeDecodeError:
            logger.warning(f"File {path} is not UTF-8 encoded, treating as binary")
            return content.decode("latin-1")  # Fallback encoding
            
    except AzureDevOpsServiceError as e:
        # Check if it's a token expiration error (401)
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while getting file content for {path}, will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token
        # Check if it's a "file not found" error - these should not be retried
        if "TF401174" in str(e) or "could not be found" in str(e).lower():
            logger.info(f"File {path} not found in repository: {e}")
            return None  # Return None to indicate file not found
        logger.error(f"Azure DevOps error getting file content for {path}: {e}")
        raise  # Re-raise other Azure DevOps errors
    except Exception as e:
        logger.error(f"Unexpected error getting file content for {path}: {e}")
        return None


def _get_all_azure_devops_files(organization: str, project_id: str, repo_id: str, commit_hash: str, access_token: str) -> List[Tuple[str, str]]:
    """Get all files in Azure DevOps repository with their types."""
    items = _list_azure_devops_items(organization, project_id, repo_id, commit_hash, access_token)
    
    files = []
    for item in items:
        if item.get("gitObjectType") == "blob":
            files.append((item["path"], "file"))
        elif item.get("gitObjectType") == "tree":
            # For directories, we just mark them as directories
            # Submodule detection happens later when we process files with .gitmodules data
            files.append((item["path"], "dir"))
    
    return files


def _is_submodule_reference(path: str, submodule_paths: Set[str]) -> bool:
    """Check if a path is a submodule reference by comparing with .gitmodules data."""
    # Check if this path matches any submodule path from .gitmodules
    return path in submodule_paths


@blitzy_exponential_retry()
def _download_azure_devops_file(
    organization: str,
    project_id: str,
    repo_id: str,
    file_path: str,
    commit_hash: str,
    access_token: str,
    repo_name: str,
    branch_name: str,
    submodule_path: Optional[str] = None
) -> Optional[BlitzyGitFile]:
    """Download a single file from Azure DevOps and write it to disk."""
    try:
        file_content = _get_azure_devops_file_content(
            organization=organization,
            project_id=project_id,
            repo_id=repo_id,
            path=file_path,
            commit_hash=commit_hash,
            access_token=access_token
        )

        # Strip leading slash from path to avoid double slashes
        clean_path = file_path.lstrip("/")
        
        # Adjust path if it's from a submodule
        if submodule_path:
            clean_path = f"{submodule_path}/{clean_path}"
        
        # Write to disk
        write_file_to_disk(
            file_path=clean_path,
            file_text=file_content,
            repo_name=repo_name,
            branch_name=branch_name
        )
        
        return BlitzyGitFile(path=clean_path, text=file_content)
        
    except Exception as e:
        # Check if it's a token expiration error (401)
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while downloading file '{file_path}', will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token
        logger.warning(f"Failed to download file '{file_path}': {e}")
        return None


def _get_submodule_files(
    submodule_repo: Dict,
    organization: str,
    project_id: str,
    submodule_path: str,
    submodule_commit_hash: str,  # This is the submodule's commit hash
    access_token: str,
    repo_name: str,
    branch_name: str
) -> List[BlitzyGitFile]:
    """Process a submodule and return downloaded files."""
    submodule_files = []

    try:
        # Get all files in submodule
        submodule_items = _list_azure_devops_items(
            organization=organization,
            project_id=project_id,
            repo_id=submodule_repo['id'],
            commit_hash=submodule_commit_hash,
            access_token=access_token
        )
        
        # Download each file in submodule
        for item in submodule_items:
            if item.get("gitObjectType") == "blob":
                file_path = item["path"]
                file_content = _get_azure_devops_file_content(
                    organization=organization,
                    project_id=project_id,
                    repo_id=submodule_repo['id'],
                    path=file_path,
                    commit_hash=submodule_commit_hash,
                    access_token=access_token
                )
                
                # Strip leading slash and handle submodule path correctly
                clean_path = file_path.lstrip("/")
                
                # Check if the file path already includes the submodule path
                if clean_path.startswith(submodule_path + "/"):
                    # The path already includes the submodule path, use it as is
                    full_path = clean_path
                else:
                    # Add submodule path prefix
                    full_path = f"{submodule_path}/{clean_path}"
                
                # Write to disk
                write_file_to_disk(
                    file_path=full_path,
                    file_text=file_content,
                    repo_name=repo_name,
                    branch_name=branch_name
                )
                
                submodule_files.append(BlitzyGitFile(path=full_path, text=file_content))
        
        logger.info(f"Downloaded {len(submodule_files)} files from submodule {submodule_path}")
        
    except Exception as e:
        logger.error(f"Failed to process submodule {submodule_path}: {e}")
        raise
    
    return submodule_files


@blitzy_exponential_retry()
def _get_submodule_commit_sha_azure_devops(
    organization: str,
    project_id: str,
    repo_id: str,
    submodule_path: str,
    commit_hash: str,
    access_token: str
) -> Optional[str]:
    """Get the commit SHA that a submodule is pointing to at a specific commit in the parent repository."""
    try:   
        # Use the existing connection function
        connection = _create_azure_connection(organization, access_token)
        git_client = connection.get_client('azure.devops.v7_1.git.git_client.GitClient')
        
        # Get the commit object using named parameters to avoid confusion
        commit = git_client.get_commit(
            repository_id=repo_id,
            project=project_id,
            commit_id=commit_hash
        )
        # Get the tree for this commit
        tree = git_client.get_tree(
            repository_id=repo_id,
            project=project_id,
            sha1=commit.tree_id,
            recursive=True
        )
        # Find the submodule entry in the tree
        for item in tree.tree_entries:
            if item.relative_path == submodule_path and item.git_object_type == 'commit':
                return item.object_id
        
        logger.warning(f"Submodule {submodule_path} not found in git tree")
        return None
        
    except Exception as e:
        # Check if it's a token expiration error (401)
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while getting submodule commit SHA for {submodule_path}, will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token
        logger.warning(f"Could not get submodule commit SHA for {submodule_path}: {e}")
        return None


def _parse_submodule_url_azure_devops(url: str) -> Optional[Dict[str, str]]:
    """Parse Azure DevOps submodule URL to extract organization, project, and repo information."""

    try:
        if 'dev.azure.com' in url:
            # Format: https://dev.azure.com/organization/project/_git/repo
            # Format: *********************:v3/organization/project/repo
            if url.startswith('git@'):
                # Handle SSH format
                parts = url.split(':v3/')[1].split('/')
                organization = parts[0]
                project = parts[1]
                repo_name = parts[2].replace('.git', '')
            else:
                # Handle HTTPS format  
                parts = url.split('/')
                if len(parts) >= 6:
                    organization = parts[3]
                    project = parts[4]
                    repo_name = parts[-1].replace('.git', '')
                else:
                    raise Exception(f"Can't parse submodule url {url}")

            return {
                'organization': organization,
                'project_name': project,
                'repo_name': repo_name,
            }

        elif 'visualstudio.com' in url:
            # Format: https://organization.visualstudio.com/project/_git/repo
            # Extract organization from subdomain
            domain_part = url.split('//')[1].split('/')[0]
            organization = domain_part.split('.')[0]

            parts = url.split('/')
            if len(parts) >= 5:
                project = parts[3]
                repo_name = parts[-1].replace('.git', '')

                return {
                    'organization': organization,
                    'project_id': project,
                    'repo_name': repo_name
                }
    except Exception as e:
        logger.error(f"Could not parse submodule URL {url}: {e}")

    return None


def _get_commit_sha_from_branch(
        organization: str, project_id: str, repo_id: str, branch_name: str, access_token: str
) -> Optional[str]:
    """Get current commit SHA for a branch in Azure DevOps repository."""
    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()
    
    try:
        # Get the branch reference
        refs = git_client.get_refs(repository_id=repo_id, project=project_id)
        
        # Find the branch reference
        branch_ref = None
        for ref in refs:
            if ref.name == f"refs/heads/{branch_name}" or ref.name == branch_name:
                branch_ref = ref
                break
        
        if branch_ref:
            logger.info(f"Found branch {branch_name} with commit SHA: {branch_ref.object_id}")
            return branch_ref.object_id
        else:
            logger.warning(f"Branch {branch_name} not found in repository")
            return None
            
    except Exception as e:
        logger.error(f"Error getting commit SHA for branch {branch_name}: {e}")
        return None


def _get_azure_devops_gitmodules_content(
    organization: str,
    project_id: str,
    repo_id: str,
    commit_hash: str,
    access_token: str
) -> Optional[str]:
    """Get .gitmodules content using SDK."""
    try:
        content = _get_azure_devops_file_content(
            organization=organization,
            project_id=project_id,
            repo_id=repo_id,
            path="/.gitmodules",
            commit_hash=commit_hash,
            access_token=access_token
        )
        if content is None:
            logger.info("No .gitmodules file found in repository")
            return None
        return content
    except AzureDevOpsServiceError as e:
        # Check if it's a "file not found" error - these should not be retried
        if "TF401174" in str(e) or "could not be found" in str(e).lower():
            logger.info(f"No .gitmodules file found in repository: {e}")
            return None
        # Check if it's a token expiration error (401) - these should be retried
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while getting .gitmodules content, will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token
        # For other Azure DevOps errors, log and re-raise
        logger.warning(f"Azure DevOps error getting .gitmodules content: {e}")
        raise
    except Exception as e:
        logger.warning(f"Unexpected error getting .gitmodules content: {e}")
        return None


def _parse_gitmodules(content: str) -> List[Dict[str, str]]:
    # Very basic parser for .gitmodules content
    submodules = []
    current = {}
    for line in content.splitlines():
        line = line.strip()
        if line.startswith("[submodule"):
            if current:
                submodules.append(current)
            current = {}
        elif '=' in line:
            key, value = [x.strip() for x in line.split('=', 1)]
            current[key] = value
    if current:
        submodules.append(current)
    return submodules


def _download_all_git_files_to_disk_azure_devops(
        repo_name: str,
        branch_name: str,
        commit_hash: str,
        git_project_repo_id: Optional[str] = None,
) -> List[BlitzyGitFile]:
    """Downloads all files from an Azure DevOps repository and returns them as BlitzyGitFiles.
    
    This function is split into smaller functions for better maintainability.
    """
    git_project_repo = _setup_repository_access(git_project_repo_id, repo_name)
    commit_hash = _get_commit_hash(git_project_repo, repo_name, branch_name, commit_hash)

    return _process_repository_files(git_project_repo, repo_name, branch_name, commit_hash)


def _setup_repository_access(git_project_repo_id: str, repo_name: str) -> GitProjectRepo:
    """Sets up initial repository access and validates credentials."""
    logger.debug("Setting up repository access")
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)
    logger.debug(
        f"Azure DevOps organization: {git_project_repo.azure_org_id}, project_id: {git_project_repo.azure_project_id}"
    )

    if not git_project_repo.access_token:
        raise Exception("Failed to get Azure DevOps access token")

    logger.debug(
        "Getting repository information for repo_id: %s, repo_name: %s, to check if repo exists",
        git_project_repo.repo_id, repo_name
    )
    repo_info = _get_azure_devops_repo(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
    )

    if not repo_info:
        raise Exception(
            f"Repository '{git_project_repo.repo_id}' not found in project '{git_project_repo.azure_project_id}'"
        )
    logger.debug("Found repository: %s", repo_info)

    return git_project_repo


def _get_commit_hash(
        git_project_repo: GitProjectRepo,
        repo_name: str,
        branch_name: str,
        commit_hash: str
) -> str:
    """Retrieves commit hash for the branch and repo if not provided."""
    if not commit_hash:
        logger.info(f"Commit hash wasn't provided will determine current commit hash for branch {branch_name}")
        commit_hash = _get_commit_sha_from_branch(
            organization=git_project_repo.azure_org_name,
            project_id=git_project_repo.azure_project_id,
            repo_id=git_project_repo.repo_id,
            branch_name=branch_name,
            access_token=git_project_repo.access_token
        )
        if not commit_hash:
            raise Exception(f"Could not get commit SHA for branch {branch_name} in repository {repo_name}")
        logger.debug(f"Found actual commit SHA for branch {branch_name}: {commit_hash}")
    return commit_hash


def _process_repository_files(
        git_project_repo: GitProjectRepo,
        repo_name: str,
        branch_name: str,
        commit_hash: str
) -> List[BlitzyGitFile]:
    """Processes and downloads all repository files including submodules."""
    logger.info(f"Downloading files from Azure DevOps repo for {repo_name}/{branch_name} (commit {commit_hash})")
    all_files: List[BlitzyGitFile] = []
    try:
        gitmodules_content = _get_gitmodules_content(git_project_repo, commit_hash)
        submodule_paths = _parse_and_get_submodule_paths(gitmodules_content)

        all_files.extend(
            _process_main_repository_files(git_project_repo, repo_name, branch_name, commit_hash, submodule_paths)
        )

        if gitmodules_content:
            logger.info("Will download files from submodules")
            submodule_files = _process_submodules(git_project_repo, gitmodules_content, repo_name, branch_name,
                                                  commit_hash)
            all_files.extend(submodule_files)

        logger.info(f"Total files downloaded: {len(all_files)}")
        return all_files

    except Exception as e:
        logger.error(f"Error during Azure DevOps download: {e}")
        raise


def _get_gitmodules_content(git_project_repo: GitProjectRepo, commit_hash: str) -> Optional[str]:
    """Gets .gitmodules content with error handling."""
    gitmodules_content = _get_azure_devops_gitmodules_content(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        commit_hash=commit_hash,
        access_token=git_project_repo.access_token,
    )
    logger.debug(f".gitmodules file content in repository: {gitmodules_content}")
    return gitmodules_content


def _parse_and_get_submodule_paths(gitmodules_content: Optional[str]) -> Set[str]:
    """Parses .gitmodules content and returns submodule paths."""
    submodule_paths = set()
    if gitmodules_content:
        submodules = _parse_gitmodules(gitmodules_content)
        submodule_paths = {submodule['path'] for submodule in submodules if 'path' in submodule}
        logger.info(f"Found {len(submodules)} submodules in repository")
    else:
        logger.info("No .gitmodules file found - repository has no submodules")
    return submodule_paths


def _process_main_repository_files(
        git_project_repo: GitProjectRepo,
        repo_name: str,
        branch_name: str,
        commit_hash: str,
        submodule_paths: Set[str]
) -> List[BlitzyGitFile]:
    """Processes and downloads files from the main repository."""
    main_files = _get_all_azure_devops_files(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        commit_hash=commit_hash,
        access_token=git_project_repo.access_token
    )
    logger.info(f"Found {len(main_files)} files in main repository")

    all_files = []
    for file_path, file_type in main_files:
        if _should_skip_file(file_path, file_type, submodule_paths):
            logger.debug(f"Skipping {file_path} while reading main repo, because it is submodule")
            continue

        file = _download_azure_devops_file(
            organization=git_project_repo.azure_org_name,
            project_id=git_project_repo.azure_project_id,
            repo_id=git_project_repo.repo_id,
            file_path=file_path,
            commit_hash=commit_hash,
            access_token=git_project_repo.access_token,
            repo_name=repo_name,
            branch_name=branch_name
        )
        if file:
            all_files.append(file)

    logger.info(f"Downloaded {len(all_files)} files from main repository")
    return all_files


def _should_skip_file(file_path: str, file_type: str, submodule_paths: Set[str]) -> bool:
    """Determines if a file should be skipped based on path and type."""
    if file_type != "file":
        return True

    if _is_submodule_reference(file_path, submodule_paths):
        logger.info(f"Skipping submodule reference: {file_path}")
        return True

    clean_path = file_path.lstrip("/")
    if clean_path in submodule_paths:
        logger.info(f"Skipping main repository file that conflicts with submodule: {file_path}")
        return True

    return False


def _process_submodules(
        git_project_repo: GitProjectRepo,
        gitmodules_content: str,
        repo_name: str,
        branch_name: str,
        commit_hash: str
) -> List[BlitzyGitFile]:
    """Processes all submodules in the repository."""
    submodules = _parse_gitmodules(gitmodules_content)
    submodule_files = []

    for submodule in submodules:
        if 'path' not in submodule or 'url' not in submodule:
            continue

        try:
            files = _process_single_submodule(
                git_project_repo,
                submodule,
                repo_name,
                branch_name,
                commit_hash
            )
            submodule_files.extend(files)
        except Exception as e:
            logger.error(f"Failed to process submodule {submodule.get('path')}: {e}")
            continue

    return submodule_files


def _process_single_submodule(
        git_project_repo: GitProjectRepo,
        submodule: Dict[str, str],
        repo_name: str,
        branch_name: str,
        commit_hash: str
) -> List[BlitzyGitFile]:
    """Processes a single submodule."""
    submodule_path = submodule['path']
    submodule_url = submodule['url']

    logger.info(f"Processing submodule: {submodule_path}")

    submodule_commit = _get_submodule_commit_sha_azure_devops(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        submodule_path=submodule_path,
        commit_hash=commit_hash,
        access_token=git_project_repo.access_token
    )

    if not submodule_commit:
        logger.info(f"Submodule {submodule_path} is defined in .gitmodules but not initialized in the repository")
        return []
    else:
        logger.info(f"Use commit {submodule_commit} for submodule {submodule_path}")

    submodule_repo_info = _parse_submodule_url_azure_devops(submodule_url)
    if not submodule_repo_info:
        logger.error(f"Could not parse submodule URL: {submodule_url}")
        return []

    project_id = _get_project_id_by_name(
        submodule_repo_info['organization'],
        submodule_repo_info['project_name'],
        git_project_repo.access_token
    )
    if not project_id:
        logger.error(
            f"Can't find project_id for project {submodule_repo_info['project_name']} "
            f"in organization {submodule_repo_info['organization']}, will skip fetchin submodule"
        )

    submodule_repo_id = _get_azure_repo_id_by_name(
        org_name=submodule_repo_info['organization'],
        project_id=project_id,
        repo_name=submodule_repo_info['repo_name'],
        access_token=git_project_repo.access_token
    )

    if not submodule_repo_id:
        logger.error(
            f"Can't find submodule repo_id for subrepo {submodule_repo_info['repo_name']} "
            f"in project {submodule_repo_info['project_name']}. Will skip this subrepo."
        )
        return []

    submodule_repo = _get_azure_devops_repo(
        organization=submodule_repo_info['organization'],
        project_id=project_id,
        repo_id=submodule_repo_id,
        access_token=git_project_repo.access_token
    )

    if not submodule_repo:
        logger.error(f"Could not access submodule repository: {submodule_url}. Will skip subrepo.")
        return []

    return _get_submodule_files(
        submodule_repo=submodule_repo,
        organization=submodule_repo_info['organization'],
        project_id=project_id,
        submodule_path=submodule_path,
        submodule_commit_hash=submodule_commit,
        access_token=git_project_repo.access_token,
        repo_name=repo_name,
        branch_name=branch_name
    )


# Azure DevOps Pull Request Functions


@blitzy_exponential_retry()
def create_single_pull_request_azure_devops(
    organization: str,
    project_id: str,
    repo_id: str,
    access_token: str,
    head_branch: str,
    base_branch: str,
    pr_title: str,
    pr_body: str,
) -> Optional[Dict]:
    """
    Create a single pull request in Azure DevOps repository.

    Args:
        organization: Azure DevOps organization name
        project_id: Project ID containing the repository
        repo_id: Repository ID
        access_token: Access token for authentication
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR
        pr_title: Title for the pull request
        pr_body: Description for the pull request

    Returns:
        Dictionary containing pull request information or None if creation failed
    """
    try:
        connection = _create_azure_connection(organization, access_token)
        git_client = connection.clients.get_git_client()

        # Ensure branch names have proper refs format
        source_ref = (
            head_branch
            if head_branch.startswith("refs/")
            else f"refs/heads/{head_branch}"
        )
        target_ref = (
            base_branch
            if base_branch.startswith("refs/")
            else f"refs/heads/{base_branch}"
        )

        # Check if PR already exists
        search_criteria = GitPullRequestSearchCriteria(
            source_ref_name=source_ref, target_ref_name=target_ref, status="active"
        )

        existing_prs = git_client.get_pull_requests(
            repository_id=repo_id, project=project_id, search_criteria=search_criteria
        )

        # Use existing PR if it exists
        if existing_prs:
            existing_pr = existing_prs[0]
            logger.info(
                f"Using existing Azure DevOps PR #{existing_pr.pull_request_id}: {existing_pr.url}"
            )
            return {
                "id": existing_pr.pull_request_id,
                "title": existing_pr.title,
                "description": existing_pr.description,
                "url": existing_pr.url,
                "status": existing_pr.status,
                "source_ref": existing_pr.source_ref_name,
                "target_ref": existing_pr.target_ref_name,
                "created_date": (
                    existing_pr.creation_date.isoformat()
                    if existing_pr.creation_date
                    else None
                ),
            }

        # Create new PR if none exists
        pr_data = {
            "sourceRefName": source_ref,
            "targetRefName": target_ref,
            "title": pr_title,
            "description": pr_body,
        }

        created_pr = git_client.create_pull_request(
            git_pull_request_to_create=pr_data,
            repository_id=repo_id,
            project=project_id,
        )

        logger.info(
            f"Created new Azure DevOps PR #{created_pr.pull_request_id}: {created_pr.url}"
        )
        return {
            "id": created_pr.pull_request_id,
            "title": created_pr.title,
            "description": created_pr.description,
            "url": created_pr.url,
            "status": created_pr.status,
            "source_ref": created_pr.source_ref_name,
            "target_ref": created_pr.target_ref_name,
            "created_date": (
                created_pr.creation_date.isoformat()
                if created_pr.creation_date
                else None
            ),
        }

    except AzureDevOpsServiceError as e:
        logger.error(f"Azure DevOps service error creating pull request: {e}")
        raise
    except Exception as e:
        logger.error(f"Error creating Azure DevOps pull request: {e}")
        raise


@blitzy_exponential_retry()
def create_all_pull_requests_azure_devops(
    git_project_repo_id: str,
    head_branch: str,
    base_branch: str = "",
    pr_title: Optional[str] = None,
    pr_body: Optional[str] = None,
    is_new_repo: bool = False,
) -> List[Dict]:
    """
    Create pull requests for the specified branch and optionally for matching branches in submodules.

    Args:
        git_project_repo_id: Repository ID for credential lookup
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR (defaults to repo's default branch)
        pr_title: Custom PR title (defaults to "Autonomous changes created by Blitzy")
        pr_body: Custom PR body (defaults to standard message)
        is_new_repo: Whether this is a new repository

    Returns:
        List of created/existing pull request dictionaries
    """
    # Get Azure DevOps credentials and repository info
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)

    # Get repository information to determine default branch if needed
    repo_info = _get_azure_devops_repo(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
    )

    if not repo_info:
        raise Exception(f"Repository '{git_project_repo.repo_id}' not found")

    # Set default base branch if not provided
    if not base_branch or is_new_repo:
        base_branch = repo_info.get("default_branch", "refs/heads/main")
        # Remove refs/heads/ prefix if present for consistency
        if base_branch.startswith("refs/heads/"):
            base_branch = base_branch[11:]

    created_prs = []

    # Create PR for main repository
    main_pr = create_single_pull_request_azure_devops(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
        head_branch=head_branch,
        base_branch=base_branch,
        pr_title=pr_title or "Autonomous changes created by Blitzy",
        pr_body=pr_body or "This PR contains automated updates created by Blitzy",
    )

    if main_pr:
        created_prs.append(main_pr)

    # Check for submodules with matching branch
    try:
        # Get .gitmodules file content from the base branch
        gitmodules_content = _get_gitmodules_content(git_project_repo, base_branch)

        if not gitmodules_content:
            logger.info("No .gitmodules file found - repository has no submodules")
            return created_prs

        # Parse .gitmodules file
        submodules = _parse_gitmodules(gitmodules_content)

        for submodule in submodules:
            if "path" not in submodule or "url" not in submodule:
                continue

            submodule_path = submodule["path"]
            submodule_url = submodule["url"]

            try:
                # Parse submodule URL to get organization, project, and repo info
                submodule_repo_info = _parse_submodule_url_azure_devops(submodule_url)
                if not submodule_repo_info:
                    logger.error(f"Could not parse submodule URL: {submodule_url}")
                    continue

                # Get project ID for the submodule
                submodule_project_id = _get_project_id_by_name(
                    submodule_repo_info["organization"],
                    submodule_repo_info["project_name"],
                    git_project_repo.access_token,
                )

                if not submodule_project_id:
                    logger.error(
                        f"Could not find project ID for submodule project: {submodule_repo_info['project_name']}"
                    )
                    continue

                # Get repository ID for the submodule
                submodule_repo_id = _get_azure_repo_id_by_name(
                    org_name=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_name=submodule_repo_info["repo_name"],
                    access_token=git_project_repo.access_token,
                )

                if not submodule_repo_id:
                    logger.error(
                        f"Could not find repository ID for submodule: {submodule_repo_info['repo_name']}"
                    )
                    continue

                # Get submodule repository info to determine default branch
                submodule_repo = _get_azure_devops_repo(
                    organization=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_id=submodule_repo_id,
                    access_token=git_project_repo.access_token,
                )

                if not submodule_repo:
                    logger.error(
                        f"Could not access submodule repository: {submodule_url}"
                    )
                    continue

                submodule_default_branch = submodule_repo.get(
                    "default_branch", "refs/heads/main"
                )
                if submodule_default_branch.startswith("refs/heads/"):
                    submodule_default_branch = submodule_default_branch[11:]

                # Create PR for submodule
                submodule_pr_title = f"Submodule update: {pr_title or 'Autonomous changes created by Blitzy'}"
                submodule_pr_body = f"Submodule changes for {submodule_path}\n\n{pr_body or 'This PR contains automated updates created by Blitzy'}"

                submodule_pr = create_single_pull_request_azure_devops(
                    organization=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_id=submodule_repo_id,
                    access_token=git_project_repo.access_token,
                    head_branch=head_branch,
                    base_branch=submodule_default_branch,
                    pr_title=submodule_pr_title,
                    pr_body=submodule_pr_body,
                )

                if submodule_pr:
                    created_prs.append(submodule_pr)

                    # Update main PR body to include submodule PR link
                    if main_pr and submodule_pr.get("url"):
                        # TODO: Update main PR description to include submodule PR link
                        # This would require implementing an update_pull_request function
                        logger.info(f"Submodule PR created: {submodule_pr['url']}")

            except Exception as submodule_error:
                logger.error(
                    f"Error processing submodule {submodule_path}: {submodule_error}"
                )
                continue

    except Exception as e:
        logger.error(f"Error processing submodules: {e}")

    return created_prs
