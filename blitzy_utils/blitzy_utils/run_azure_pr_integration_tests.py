#!/usr/bin/env python3
"""
Azure DevOps Pull Request Integration Test Runner

This script runs integration tests for the Azure DevOps pull request functionality
implemented in blitzy_utils. It follows the testing patterns from archie-github-handler.

Usage:
    python run_azure_pr_integration_tests.py
    
Environment Variables:
    AZURE_DEVOPS_ACCESS_TOKEN: Required - Azure DevOps personal access token
    AZURE_DEVOPS_ORG: Optional - Azure DevOps organization name (default: azureblitzy1)
    AZURE_DEVOPS_PROJECT_ID: Optional - Project ID for testing
    AZURE_DEVOPS_REPO_ID: Optional - Repository ID for testing

Example:
    export AZURE_DEVOPS_ACCESS_TOKEN="your_token_here"
    python run_azure_pr_integration_tests.py
"""

import os
import sys
from pathlib import Path

root_path = Path(__file__).parent.parent
sys.path.insert(0, str(root_path))

def setup_environment():
    """Setup environment variables for testing."""
    # Check for required environment variables
    access_token = os.getenv('AZURE_DEVOPS_ACCESS_TOKEN','****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************')
    if not access_token:
        print("❌ Error: AZURE_DEVOPS_ACCESS_TOKEN environment variable is required")
        print("\nTo set up your access token:")
        print("1. Go to https://dev.azure.com/[your-org]/_usersSettings/tokens")
        print("2. Create a new Personal Access Token with 'Code (read & write)' permissions")
        print("3. Export it as an environment variable:")
        print("   export AZURE_DEVOPS_ACCESS_TOKEN='your_token_here'")
        print("4. Run this script again")
        return False

    # Set up test configuration with environment variables or defaults
    test_config = {
        'organization': os.getenv('AZURE_DEVOPS_ORG', 'azureblitzy1'),
        'project_id': os.getenv('AZURE_DEVOPS_PROJECT_ID', '92381f74-980b-4868-a402-90a78cb346ec'),
        'repo_id': os.getenv('AZURE_DEVOPS_REPO_ID', 'e2e0696f-9d4c-4f3d-aefc-dfa3de642276'),
        'access_token': access_token,
        'test_branch': os.getenv('AZURE_DEVOPS_TEST_BRANCH', 'test-pr-branch'),
        'base_branch': os.getenv('AZURE_DEVOPS_BASE_BRANCH', 'main'),
        'git_project_repo_id': os.getenv('AZURE_DEVOPS_GIT_PROJECT_REPO_ID', 'f47ac10b-58cc-4372-a567-0e02b2c3d479'),
        'user_id': os.getenv('AZURE_DEVOPS_USER_ID', 'eba17c5f-a04f-4e79-b512-7cec8a406e15')
    }

    # Update the test configuration in the test module
    try:
        sys.path.insert(0, str(root_path))
        from blitzy_utils.test.test_azure_pull_requests_integration import TEST_CONFIG
        TEST_CONFIG.update(test_config)
        print("✅ Test environment configured successfully")
        print(f"   Organization: {test_config['organization']}")
        print(f"   Project ID: {test_config['project_id']}")
        print(f"   Repository ID: {test_config['repo_id']}")
        print(f"   Test Branch: {test_config['test_branch']}")
        print(f"   Base Branch: {test_config['base_branch']}")
        return True
    except ImportError as e:
        print(f"❌ Error importing test module: {e}")
        return False

def run_basic_tests():
    """Run basic integration tests."""
    try:
        # Ensure root_path is in sys.path to import test module
        sys.path.insert(0, str(Path(__file__).parent.parent))
        from blitzy_utils.test.test_azure_pull_requests_integration import run_all_integration_tests

        print("\n" + "="*60)
        print("RUNNING BASIC AZURE DEVOPS PULL REQUEST INTEGRATION TESTS")
        print("="*60)
        
        # Run basic tests
        basic_tests = [
            'test_create_single_pull_request_azure_devops',
            'test_pull_request_parameter_validation',
            'test_branch_reference_formatting'
        ]
        
        results = run_all_integration_tests(
            suppress_logging=False,
            run_specific=basic_tests
        )
        
        return results['passed'] == results['total']
        
    except Exception as e:
        print(f"❌ Error running basic tests: {e}")
        return False

def run_comprehensive_tests():
    """Run comprehensive integration tests."""
    try:
        sys.path.insert(0, str(Path(__file__).parent.parent))
        from blitzy_utils.test.test_azure_pull_requests_integration import run_all_integration_tests

        print("\n" + "="*60)
        print("RUNNING COMPREHENSIVE AZURE DEVOPS PULL REQUEST INTEGRATION TESTS")
        print("="*60)
        
        # Run all tests
        results = run_all_integration_tests(suppress_logging=False)
        
        return results['passed'] == results['total']
        
    except Exception as e:
        print(f"❌ Error running comprehensive tests: {e}")
        return False

def run_specific_test(test_name):
    """Run a specific test by name."""
    try:
        sys.path.insert(0, str(Path(__file__).parent.parent))
        from blitzy_utils.test.test_azure_pull_requests_integration import run_all_integration_tests

        print(f"\n" + "="*60)
        print(f"RUNNING SPECIFIC TEST: {test_name}")
        print("="*60)
        
        results = run_all_integration_tests(
            suppress_logging=False,
            run_specific=[test_name]
        )
        
        return results['passed'] == results['total']
        
    except Exception as e:
        print(f"❌ Error running specific test: {e}")
        return False

def main():
    """Main function to run integration tests."""
    print("Azure DevOps Pull Request Integration Test Runner")
    print("=" * 50)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'basic':
            success = run_basic_tests()
        elif command == 'comprehensive' or command == 'all':
            success = run_comprehensive_tests()
        elif command.startswith('test_'):
            success = run_specific_test(command)
        elif command == 'help':
            print("\nUsage:")
            print("  python run_azure_pr_integration_tests.py [command]")
            print("\nCommands:")
            print("  basic         - Run basic integration tests")
            print("  comprehensive - Run all integration tests")
            print("  all           - Same as comprehensive")
            print("  test_*        - Run specific test by name")
            print("  help          - Show this help message")
            print("\nExamples:")
            print("  python run_azure_pr_integration_tests.py basic")
            print("  python run_azure_pr_integration_tests.py test_create_single_pull_request_azure_devops")
            sys.exit(0)
        else:
            print(f"❌ Unknown command: {command}")
            print("Use 'help' for usage information")
            sys.exit(1)
    else:
        # Default to basic tests
        success = run_basic_tests()
    
    # Exit with appropriate code
    if success:
        print("\n🎉 All tests passed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Check the output above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
