import os
import time
import threading
import requests
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from typing import List, Dict, Optional, Tuple, Any, Set, Union
from dataclasses import dataclass
from queue import Queue

from github import Github, GithubException, InputGitTreeElement
from github.Repository import Repository
from github.GitCommit import GitCommit
from github.PullRequest import PullRequest
from github.GitRef import GitRef
from github.ContentFile import ContentFile

from blitzy_utils.azure_utils import (
    _download_all_git_files_to_disk_azure_devops,
    _get_azure_devops_credentials_by_repo_id, _get_azure_devops_repo, _create_azure_devops_repo,
    AzureRepo
)


from blitzy_utils.errors import FailedToFetchCredentials
from blitzy_utils.logger import logger
from blitzy_utils.consts import GITHUB_CREATE_REPO_DESCRIPTION, SvcType
from blitzy_utils.common import get_google_authorized_request_headers, blitzy_exponential_retry, BlitzyGitFile
from blitzy_utils.disk import write_file_to_disk
from blitzy_utils.service_client import ServiceClient

GitRepo = Union[AzureRepo, Repository]

@blitzy_exponential_retry()
def _get_service_type(user_id: str, git_project_repo_id: str) -> SvcType:
    """Get the service type (GitHub or Azure DevOps) for a repository."""
    try:
        github_handler_server = os.environ.get('SERVICE_URL_GITHUB')
        
        url = f"{github_handler_server}/v1/users/{user_id}/repositories/{git_project_repo_id}/svc-type"
        logger.info(f"Fetching service type from {url}")
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            svc_type_str = data.get('svcType')
            if not svc_type_str:
                raise Exception(f"Service type not found in response for git_project_repo_id {git_project_repo_id}")
            
            logger.info(f"Service type: {svc_type_str}")
            return SvcType(svc_type_str)
        else:
            err_msg = (
                f"Failed to fetch service type for git_project_repo_id {git_project_repo_id}. "
                f"Status code: {response.status_code}, response: {response.text}"
            )
            raise Exception(err_msg)
    except Exception as e:
        logger.error(f"Error fetching service type: {e}")
        raise

class GitHubRateLimiter:
    """
    Rate limiter that respects GitHub's rate limit headers.
    Monitors X-RateLimit-Remaining and X-RateLimit-Reset headers.
    """

    def __init__(self, min_remaining: int = 100):
        self.lock = threading.Lock()
        self.min_remaining = min_remaining
        self.rate_limit_reset = 0
        self.rate_limit_remaining = None
        self.request_queue = Queue()

    def update_from_headers(self, headers: Dict[str, str]):
        """Update rate limit info from GitHub response headers."""
        with self.lock:
            if 'X-RateLimit-Remaining' in headers:
                self.rate_limit_remaining = int(headers['X-RateLimit-Remaining'])
            if 'X-RateLimit-Reset' in headers:
                self.rate_limit_reset = int(headers['X-RateLimit-Reset'])

    def wait_if_needed(self):
        """Wait if we're approaching rate limit."""
        with self.lock:
            if self.rate_limit_remaining is not None and self.rate_limit_remaining < self.min_remaining:
                wait_time = self.rate_limit_reset - time.time()
                if wait_time > 0:
                    logger.warning(f"Approaching rate limit. Waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time + 1)  # Add 1 second buffer


@dataclass
class RepoMetadata:
    """Cached repository metadata to avoid repeated API calls"""
    submodule_paths: Set[str]
    gitmodules_content: Optional[str]

    @classmethod
    def from_repo(cls, repo: Repository, ref: str) -> 'RepoMetadata':
        """Create RepoMetadata by fetching from repository once"""
        try:
            gitmodules_text = get_gitmodules_content(repo=repo, ref=ref)
            submodules = parse_gitmodules(gitmodules_text)
            return cls(
                submodule_paths=set(submodules.keys()),
                gitmodules_content=gitmodules_text
            )
        except:
            # No gitmodules file
            return cls(submodule_paths=set(), gitmodules_content=None)


@dataclass
class DownloadTask:
    """Represents a file download task."""
    repo: Repository
    file_path: str
    ref: str
    branch_name: str
    output_path: str
    parent_repo_name: str
    submodule_path: Optional[str] = None
    repo_metadata: Optional[RepoMetadata] = None


@blitzy_exponential_retry()
def get_user_secret_info(user_id: str, server: str):
    """Fetch GitHub access token from the secret server."""
    try:
        url = f"{server}/secret/{user_id}"
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            return data.get('accessToken'), data.get('installationID')
        else:
            logger.error(f"Failed to fetch access token. Status code: {response.status_code}")
            return None, None
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        return None, None


@blitzy_exponential_retry()
def get_github_installation_secret_info(installation_id: str, server: str):
    """Fetch GitHub access token from the github handler."""
    try:
        url = f"{server}/v1/github/secret/{installation_id}"
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            return data.get('accessToken'), data.get('installationID')
        else:
            logger.error(f"Failed to fetch access token. Status code: {response.status_code}")
            return None, None
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        return None, None


@blitzy_exponential_retry()
def get_github_installations(access_token: str):
    """Get GitHub installations for the authenticated user."""
    headers = {
        'Accept': 'application/vnd.github+json',
        'Authorization': f'Bearer {access_token}',
        'X-GitHub-Api-Version': '2022-11-28'
    }

    response = requests.get(
        'https://api.github.com/user/installations',
        headers=headers
    )

    if response.status_code == 200:
        data = response.json()
        return data.get('installations')
    else:
        logger.error(f"Failed to get installations. Status code: {response.status_code}")
        return None


@blitzy_exponential_retry()
def get_credentials_by_repo_id(repo_id: str) -> Tuple[str, str]:
    """Get GitHub credentials by repo id using github_handler service"""

    github_handler_server = os.environ.get('SERVICE_URL_GITHUB')
    if not github_handler_server:
        raise Exception("SERVICE_URL_GITHUB not set, can't fetch credentials by repo id. Please set it.")
    try:
        url = f"{github_handler_server}/v1/github/repositories/{repo_id}/secret/access-token"
        logger.info(f"Fetching credentials by repo id from {url}")
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers, timeout=60)
        if response.status_code == 200:
            logger.debug("Successfully fetched credentials by repo id")
            data = response.json()
            logger.debug(f"GitHub credentials response: {data}")
            return data['accessToken'], data['installationID']
        else:
            err_msg = (f"Failed to fetch credentials from {url} for repo id {repo_id}. "
                       f"Status code: {response.status_code}, response: {response.text}")
            raise FailedToFetchCredentials(err_msg)
    except FailedToFetchCredentials:
        raise  # this is an exception we just raised, no need to process just reraise
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        raise FailedToFetchCredentials(
            f"Failed to fetch credentials from repo id {repo_id}, error {e}"
        ) from e

def _get_token_and_installation_id(server: str, user_id: Optional[str], repo_id: Optional[str]) -> Tuple[str, str]:
    """
    This function is a temporary wrapper around getting access token and installation ID call. We are currently
    migrating to shared github repository and will use repo_id to get the access token and installation ID instead
    of user_id. But blitzy_utils being used in many places, and we can't make change an atomic operation.
    """
    if repo_id and os.environ.get('SERVICE_URL_GITHUB'):
        logger.info(f"Using repo id {repo_id} for getting access token and installation ID.")
        access_token, installation_id = get_credentials_by_repo_id(repo_id=repo_id)
    else:
        logger.info(f"Using user id {user_id} for getting access token and installation ID.")
        access_token, installation_id = get_user_secret_info(user_id=user_id, server=server)
    return access_token, installation_id


def get_github_repo(
        repo_name: str, user_id: str, server: str, create=True, repo_id=None, repo_project_id=None
) -> Tuple[GitRepo, bool]:
    """
    Function fetches and returns repo information for github or azure repo. In case of gihub
    the repo type is github.Repository. In case of azure repo type is .common.AzureRepo

    If the variable repo_project_id is set, then we will archie github handler to determine repo type, otherwise
    we fall back to default behavior and assume repo type is github.

    IMPORTANT!!!!! If the variable `repo_project_id` is set, we will ignore all
    other variables for azure and fetch them archie-github-handler. We will keep using these variables for github for now.
    """
    if repo_project_id:
        logger.info(
            f"Variable repo_project_id is set to {repo_project_id}, will determine repo type using github-handler serivce"
        )
        svc_type = _get_service_type(user_id, repo_project_id)
        if svc_type == SvcType.GITHUB:
            logger.info(
                f"Repo project {repo_project_id} is github repo type, will use github API to get repo info"
            )
            return get_github_repo_github(repo_name, user_id, server, create, repo_id)
        elif svc_type == SvcType.AZURE_DEVOPS:
            logger.info(
                f"Repo project {repo_project_id} is azure repo type, will use azure API to get repo info"
            )
            git_project_repo = _get_azure_devops_credentials_by_repo_id(repo_project_id)

            logger.info(
                f"Fetched github_project_repo details org_id: {git_project_repo.azure_org_id}, "
                f"azure_project_id: {git_project_repo.azure_project_id}, "
                f"repo_id: {git_project_repo.repo_id}"
            )
            remote_azure_repo = _get_azure_devops_repo(git_project_repo.azure_org_name,
                                                       git_project_repo.azure_project_id,
                                                       git_project_repo.repo_id, git_project_repo.access_token
                                                       )
            if not remote_azure_repo:
                logger.info(
                    f"Didn't find repo {repo_name} in azure devops org {git_project_repo.azure_org_name} project "
                    f"{git_project_repo.azure_project_id}, "
                    f"will create repo"
                )
                new_repo_data = _create_azure_devops_repo(git_project_repo.azure_org_name,
                                                          git_project_repo.azure_project_id, repo_name,
                                                          git_project_repo.access_token)
                repo_id = new_repo_data['id']
                new_repo = True
            else:
                logger.debug("Azure repo exists, will not create new repo")
                repo_id = remote_azure_repo['id']
                new_repo = False

            azure_repo = AzureRepo(
                org_id=git_project_repo.azure_org_id,
                project_id=git_project_repo.azure_project_id,
                repo_id=repo_id
            )
            return azure_repo, new_repo
        else:
            raise Exception(f"Unknown service type {svc_type}")

    else:
        logger.info(
            f"Variable repo_project_id is not set, assume github repo type")
        return get_github_repo_github(repo_name, user_id, server, create, repo_id)


@blitzy_exponential_retry()
def get_github_repo_github(repo_name: str, user_id: str, server: str, create=True, repo_id=None) -> Tuple[Repository, bool]:
    """Get or create a GitHub repository using GitHub App authentication.

    Handles both user and organization repositories based on the installation target type.
    """
    # Get user access token
    access_token, installation_id = _get_token_and_installation_id(server=server, user_id=user_id, repo_id=repo_id)

    repo_metadata = get_repo_info_by_id(repo_id=repo_id)
    if not access_token:
        raise Exception("Failed to get GitHub access token")

    g = Github(access_token)

    login_name = repo_metadata["orgName"]
    target_type = repo_metadata["installationType"]

    full_repo_name = f"{login_name}/{repo_name}"

    repo = None
    is_new_repo = False

    repo_options = {
        "name": repo_name,
        "private": True,
        "description": GITHUB_CREATE_REPO_DESCRIPTION,
        "has_issues": True,
        "has_projects": True,
        "has_wiki": True,
        "auto_init": True  # Initialize with README
    }

    try:
        repo = g.get_repo(full_repo_name)
    except GithubException as e:
        if e.status == 404 and create:
            logger.warning(f'Repository does not exist: {repo_name}')
            if create:
                logger.info(f'Creating new repository: {repo_name}')
                if target_type == 'Organization':
                    org = g.get_organization(login_name)
                    # Create new organization repository

                    repo = org.create_repo(**repo_options)
                else:
                    user = g.get_user()
                    repo = user.create_repo(**repo_options)
                is_new_repo = True
        else:
            logger.error(f'Failed to fetch repo: {full_repo_name}')
            raise

    return repo, is_new_repo


def download_single_file(repo_name: str, user_id: str, server: str,
                         file_path: str, commit_hash: str, repo_id: Optional[str] = None) -> Optional[str]:
    """Download a single file from the repository at stored commit hash, with submodule support."""
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id,
                                     server=server, create=False, repo_id=repo_id)
    if not commit_hash:
        raise ValueError("Invalid commit hash")

    try:
        # First attempt: try to get file content directly from the main repository
        file_content = get_contents_with_retry(repo=github_repo, path=file_path, ref=commit_hash)
        if isinstance(file_content, list):
            logger.warning(f'Attempted to download folder instead of file: {file_path}')
            return None
        logger.info(f'Downloaded file {file_path} from GitHub into memory')
        return decode_file_content(content_file=file_content)

    except GithubException as e:
        # If file not found (404), check if it's in a submodule
        if e.status == 404:
            logger.info(f"File {file_path} not found in main repo, checking submodules...")

            try:
                submodule_repo, relative_path, matching_submodule_path = get_submodule_for_file(
                    repo=github_repo,
                    user_id=user_id,
                    server=server,
                    file_path=file_path,
                    head_commit_hash=commit_hash
                )

                if not submodule_repo:
                    return None

                # Get the submodule commit hash at this specific commit in the main repo
                try:
                    # Get the commit object for the submodule at this specific commit in parent repo
                    submodule_commit_entry = get_contents_with_retry(
                        repo=github_repo, path=matching_submodule_path, ref=commit_hash)
                    submodule_commit_hash = submodule_commit_entry.sha

                    # Try to get the file content using the submodule's commit hash
                    submodule_file = get_contents_with_retry(
                        repo=submodule_repo, path=relative_path, ref=submodule_commit_hash)
                    logger.info(f'Downloaded file {relative_path} at commit {submodule_commit_hash}')
                    return decode_file_content(content_file=submodule_file)
                except GithubException as commit_error:
                    logger.warning(f"Could not get file from submodule at specific commit: {commit_error}")
                    return None

            except Exception as submodule_error:
                logger.error(f"Error processing submodules: {submodule_error}")
                return None
        else:
            logger.error(f"Error downloading {file_path} at commit {commit_hash}: {e}")
            return None


@blitzy_exponential_retry()
def decode_file_content(content_file: ContentFile):
    """
    Safely decode file content from GitHub API.
    Returns None for submodule references.
    """

    try:
        # Check if this is a large file (encoding is None)
        if content_file.content == '' or content_file.encoding == 'none':
            # Large file - use download_url
            logger.info(f'Large file detected ({content_file.size} bytes), downloading from URL')
            response = requests.get(content_file.download_url)
            response.raise_for_status()

            # Try to decode as text
            try:
                return response.text
            except UnicodeDecodeError:
                # Try other encodings
                encodings = ['latin-1', 'cp1252', 'utf-16', 'ascii']
                for encoding in encodings:
                    try:
                        return response.content.decode(encoding)
                    except UnicodeDecodeError:
                        continue

                logger.error(f'Could not decode file {content_file.path}, all encodings failed')
                return None
        else:
            # Regular file - use decoded_content (PyGithub handles the encoding)
            decoded_bytes = content_file.decoded_content

            # Try UTF-8 decoding first
            try:
                return decoded_bytes.decode('utf-8')
            except UnicodeDecodeError:
                # Try other common encodings
                encodings = ['latin-1', 'cp1252', 'utf-16', 'ascii']
                for encoding in encodings:
                    try:
                        return decoded_bytes.decode(encoding)
                    except UnicodeDecodeError:
                        continue

                # If all text decodings fail, return as binary string representation
                # or handle binary files differently
                logger.error(f'Could not decode file {content_file.path}, all encodings failed')
                return None  # Return raw bytes for binary files

    except Exception as e:
        logger.error(f'Could not decode file {content_file.path}: {e}')
        return None


def parse_gitmodules(content: str) -> dict:
    """Parse .gitmodules content and return a dictionary mapping paths to repository information."""
    submodules = {}
    current_submodule = None

    lines = content.strip().split('\n')
    for line in lines:
        line = line.strip()
        if not line or line.startswith('#'):
            continue

        if line.startswith('[submodule "'):
            # Extract submodule name between quotes
            name = line[line.find('"')+1:line.rfind('"')]
            current_submodule = name
            submodules[current_submodule] = {'name': current_submodule}
        elif current_submodule and '=' in line:
            key, value = [part.strip() for part in line.split('=', 1)]
            submodules[current_submodule][key] = value

    # Reorganize to use path as the key
    result = {}
    for name, info in submodules.items():
        if 'path' in info and 'url' in info:
            result[info['path']] = {
                'name': name,
                'url': info['url']
            }

    return result


def ssh_to_https_url(ssh_url: str) -> str:
    """Convert a Git SSH URL to HTTPS URL, supporting both GitHub.com and GitHub Enterprise."""
    # Handle standard GitHub SSH URL (**************:owner/repo.git)
    if ssh_url.startswith('**************:'):
        path = ssh_url[15:]  # Remove '**************:'
        return f"https://github.com/{path}"

    # Handle GitHub Enterprise SSH URL (************************:owner/repo.git)
    elif ssh_url.startswith('git@') and ':' in ssh_url:
        # Extract the domain and path
        domain_part = ssh_url[4:ssh_url.find(':')]  # Extract domain part (github.mycompany.com)
        path = ssh_url[ssh_url.find(':')+1:]        # Extract path part (owner/repo.git)
        return f"https://{domain_part}/{path}"

    # Handle SSH protocol URLs (ssh://**************/owner/repo.git)
    elif ssh_url.startswith('ssh://git@'):
        # Remove 'ssh://git@' prefix
        server_path = ssh_url[10:]
        # Split into server and path parts
        server, *path_parts = server_path.split('/', 1)
        path = path_parts[0] if path_parts else ''
        return f"https://{server}/{path}"

    # If it's already an HTTPS URL or another format, return as is
    return ssh_url


def extract_repo_info_from_url(url: str) -> Tuple[str, str, str]:
    """Extract owner, repo name, and domain from a GitHub URL."""
    # Convert to HTTPS if it's SSH
    https_url = ssh_to_https_url(url)

    # Check if it's an HTTPS URL
    if https_url.startswith('https://'):
        # Extract the domain and path
        url_parts = https_url[8:].split('/', 1)
        if len(url_parts) < 2:
            return '', '', url_parts[0] if url_parts else ''

        domain = url_parts[0]  # e.g., github.com or github.mycompany.com
        path = url_parts[1]    # e.g., owner/repo.git

        # Remove .git extension if present
        if path.endswith('.git'):
            path = path[:-4]

        # Split by / to get owner and repo
        parts = path.split('/')
        if len(parts) >= 2:
            return parts[0], parts[1], domain

    # Return empty strings if parsing fails
    return '', '', ''


def get_all_github_file_paths(
    repo_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None
) -> List[str]:
    # deprecated - see download_all_git_files_to_disk
    """
    Get all file paths from a GitHub repository, optionally including files in submodules.

    Args:
        repo_name: Name of the repository
        user_id: User ID for authentication
        server: Server URL for authentication
        branch_name: Branch to get files from
        include_submodules: Whether to include files in submodules
        repo_id: ID of the repository, if available(will be used to fetch credentials)

    Returns:
        List of file paths
    """
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id,
                                     server=server, create=False, repo_id=repo_id)
    all_files = []

    def get_contents(repo: Repository, path: str, commit_hash: str) -> List[str]:
        contents = []
        try:
            items = get_contents_with_retry(repo=repo, path=path, ref=commit_hash)

            # Handle single file
            if not isinstance(items, list):
                items = [items]

            for item in items:
                if item.type == "dir":
                    contents.extend(get_contents(repo, item.path, commit_hash))
                elif item.type == "file":
                    contents.append(item.path)
        except GithubException as e:
            logger.error(f"Error accessing {path}: {e}")
            return []

        return contents

    # Get files in the main repository
    main_files = get_contents(github_repo, "", commit_hash)
    all_files.extend(main_files)

    try:
        gitmodules_text = get_gitmodules_content(repo=github_repo, ref=commit_hash)
        submodules = parse_gitmodules(gitmodules_text)

        # Process each submodule
        for submodule_path, info in submodules.items():
            # Get submodule commit hash
            submodule_commit = get_submodule_commit_sha(
                repo=github_repo,
                submodule_path=submodule_path,
                commit_hash=commit_hash)
            if not submodule_commit:
                logger.warning(f"Couldn't determine commit for submodule {submodule_path}")
                continue

            # Get the submodule repo
            submodule_url = info['url']
            https_url = ssh_to_https_url(submodule_url)
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

            if not submodule_owner or not submodule_repo_name:
                logger.error(f"Could not extract owner/repo from URL: {https_url}")
                continue

            # Get access token and create GitHub client
            access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

            if domain and domain != 'github.com':
                g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
            else:
                g = Github(access_token)

            try:
                submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")

                # Get all files in the submodule
                submodule_files = get_contents(submodule_repo, "", submodule_commit)

                # Add submodule path prefix to all files
                for file_path in submodule_files:
                    all_files.append(f"{submodule_path}/{file_path}")

                logger.info(f"Added {len(submodule_files)} files from submodule {submodule_path}")

            except GithubException as e:
                logger.error(f"Error accessing submodule repo: {e}")
                raise

    except GithubException as e:
        logger.warning(f"No .gitmodules file found: {e}")

    return all_files


@blitzy_exponential_retry()
def get_contents_with_retry(repo: Repository, path: str, ref: str):
    """Wrapper function to get contents with retry logic"""
    return repo.get_contents(path, ref=ref)


@blitzy_exponential_retry()
def get_gitmodules_content(repo: Repository, ref: str) -> str:
    """Get .gitmodules content with retry logic"""
    gitmodules_content = repo.get_contents(".gitmodules", ref=ref)
    return decode_file_content(content_file=gitmodules_content)


@blitzy_exponential_retry()
def get_submodule_repo(github_client: Github, owner: str, repo_name: str):
    """Get submodule repository with retry logic"""
    return github_client.get_repo(f"{owner}/{repo_name}")


@blitzy_exponential_retry()
def get_submodule_commit_sha_with_retry(repo: Repository, submodule_path: str, commit_hash: str) -> Optional[str]:
    """Get submodule commit SHA with retry logic"""
    commit_obj = get_git_commit(sha=commit_hash, repo=repo)
    tree = repo.get_git_tree(commit_obj.tree.sha, recursive=True)

    for item in tree.tree:
        if item.path == submodule_path and item.type == 'commit':
            logger.info(f"Found submodule {submodule_path} with SHA {item.sha}")
            return item.sha

    return None


def is_submodule_reference(content_file: ContentFile, submodule_paths: Set[str]) -> bool:
    """
    Check if a ContentFile object represents a submodule reference.
    Now accepts pre-fetched submodule_paths to avoid repeated API calls.
    """
    # Check if it's explicitly marked as submodule
    if content_file.path in submodule_paths or (hasattr(content_file, 'type') and content_file.type == 'submodule'):
        return True

    return False


@blitzy_exponential_retry()
def download_file_with_rate_limit(
    task: DownloadTask,
    rate_limiter: GitHubRateLimiter
) -> Optional[BlitzyGitFile]:
    """
    Download a single file with rate limiting.
    Returns None if the file is a submodule reference.
    """
    # Wait if approaching rate limit
    rate_limiter.wait_if_needed()

    # Get file content
    file_content = get_contents_with_retry(task.repo, path=task.file_path, ref=task.ref)

    # Use cached submodule paths instead of fetching every time
    submodule_paths = task.repo_metadata.submodule_paths if task.repo_metadata else set()

    # Check if it's a submodule reference using cached data
    if is_submodule_reference(content_file=file_content, submodule_paths=submodule_paths):
        logger.info(f"Skipping submodule reference during download: {task.file_path}")
        return None

    # Update rate limiter from response headers if available
    if hasattr(file_content, '_rawData') and hasattr(file_content._rawData, 'headers'):
        rate_limiter.update_from_headers(file_content._rawData.headers)

    # Decode content
    content = decode_file_content(content_file=file_content)

    # Skip if content is None (e.g., submodule or decode failure)
    if content is None:
        logger.warning(f"Skipping file {task.file_path} - no decodable content")
        return None

    # Adjust path if it's from a submodule
    path = task.output_path
    if task.submodule_path:
        path = f"{task.submodule_path}/{path}"

    # Create BlitzyGitFile object
    git_file = BlitzyGitFile(
        path=path,
        text=content
    )

    # Write to disk
    write_file_to_disk(
        file_path=git_file.path,
        file_text=git_file.text,
        repo_name=task.parent_repo_name,
        branch_name=task.branch_name
    )

    logger.info(f"Successfully downloaded: {git_file.path}")

    return git_file


def get_submodule_paths(repo: Repository, ref: str) -> set:
    """Get all known submodule paths from .gitmodules"""
    try:
        gitmodules_text = get_gitmodules_content(repo=repo, ref=ref)
        submodules = parse_gitmodules(gitmodules_text)
        return set(submodules.keys())
    except:
        return set()


def get_all_files_in_repo(repo: Repository, ref: str, path: str = "", submodule_paths: Set[str] = None) -> List[Tuple[str, str]]:
    """
    Recursively get all file paths and their types in a repository.
    Returns list of (path, type) tuples where type can be 'file', 'dir', or 'submodule'.
    """
    files = []

    try:
        contents = get_contents_with_retry(repo=repo, path=path, ref=ref)

        if not isinstance(contents, list):
            contents = [contents]

        for content in contents:
            # Check if this path is a known submodule
            if submodule_paths and content.path in submodule_paths:
                files.append((content.path, "submodule"))
                logger.info(f"Found submodule: {content.path}")
            elif content.type == "file":
                files.append((content.path, "file"))
            elif content.type == "dir":
                # Recursively get files in subdirectory
                subfiles = get_all_files_in_repo(repo, ref, content.path, submodule_paths)
                files.extend(subfiles)

    except Exception as e:
        logger.error(f"Error listing files in {path}: {e}")
        raise

    return files


def download_all_git_files_to_disk(
    repo_name: str,
    branch_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
    max_workers: int = 10,
    rate_limit_threshold: int = 100,
    git_project_repo_id: Optional[str] = None,
) -> List[BlitzyGitFile]:
    """
    Download all files from a git repository including submodules.
    Supports both GitHub and Azure DevOps repositories.
    Network errors will be retried at the file level, not the entire operation.
    If any file fails after all retries, the entire operation fails.
    """
    # Get the service type for this repository
    logger.info(f"Downloading all files from {repo_name}/{branch_name} "
                f"with commit hash {commit_hash} where user_id is {user_id} and repo_id is {repo_id}")
    if not git_project_repo_id:
        logger.info("Git project Repo ID not provided, using default gihub service type")
        svc_type = SvcType.GITHUB
    else:
        logger.info(f"Git project Repo ID {git_project_repo_id}, will determine repo type")
        svc_type = _get_service_type(user_id=user_id, git_project_repo_id=git_project_repo_id)
        logger.info(f"Processing repository {repo_name} with service type: {svc_type.value}")

    if svc_type == SvcType.GITHUB:
        return _download_all_git_files_to_disk_github(
            repo_name=repo_name,
            branch_name=branch_name,
            user_id=user_id,
            server=server,
            commit_hash=commit_hash,
            repo_id=repo_id,
            max_workers=max_workers,
            rate_limit_threshold=rate_limit_threshold
        )
    elif svc_type == SvcType.AZURE_DEVOPS:
        return _download_all_git_files_to_disk_azure_devops(
            repo_name=repo_name,
            branch_name=branch_name,
            commit_hash=commit_hash,
            git_project_repo_id=git_project_repo_id
        )
    else:
        raise ValueError(f"Unsupported service type: {svc_type}")


def _download_all_git_files_to_disk_github(
    repo_name: str,
    branch_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
    max_workers: int = 10,
    rate_limit_threshold: int = 100
) -> List[BlitzyGitFile]:
    """
    Download all files from a GitHub repository including submodules.
    Network errors will be retried at the file level, not the entire operation.
    If any file fails after all retries, the entire operation fails.
    """
    try:
        github_repo, _ = get_github_repo(
            repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id
        )
    except Exception as e:
        logger.error(f"Failed to get repository {repo_name}: {e}")
        raise

    all_files = []
    rate_limiter = GitHubRateLimiter(min_remaining=rate_limit_threshold)

    # Fetch repository metadata ONCE
    logger.info("Fetching repository metadata...")
    repo_metadata = RepoMetadata.from_repo(github_repo, commit_hash)

    # Create thread pool for parallel downloads
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []

        # Get all files in main repository
        logger.info("Listing files in main repository...")
        try:
            # Pass cached submodule paths
            main_files_list = get_all_files_in_repo(
                github_repo,
                commit_hash,
                submodule_paths=repo_metadata.submodule_paths
            )
            logger.info(f"Found {len(main_files_list)} files in main repository")

            # Submit download tasks for main repository files (skip submodules)
            for file_path, file_type in main_files_list:
                if file_type == "submodule":
                    logger.info(f"Skipping submodule reference: {file_path}")
                    continue

                task = DownloadTask(
                    repo=github_repo,
                    file_path=file_path,
                    ref=commit_hash,
                    output_path=file_path,
                    branch_name=branch_name,
                    repo_metadata=repo_metadata,  # Pass cached metadata
                    parent_repo_name=github_repo.name
                )
                future = executor.submit(download_file_with_rate_limit, task, rate_limiter)
                futures.append(future)

        except Exception as e:
            logger.error(f"Failed to list main repository files: {e}")
            raise

        # Process submodules using cached gitmodules content
        if repo_metadata.gitmodules_content:
            try:
                submodules = parse_gitmodules(repo_metadata.gitmodules_content)

                # Process each submodule in parallel
                submodule_futures = []

                for submodule_path, info in submodules.items():
                    # Submit submodule processing as a separate task
                    future = executor.submit(
                        process_submodule_parallel,
                        github_repo, submodule_path, info, commit_hash, branch_name,
                        user_id, server, repo_id, rate_limiter, executor
                    )
                    submodule_futures.append((submodule_path, future))

                # Collect submodule results
                for submodule_path, future in submodule_futures:
                    try:
                        submodule_tasks = future.result()
                        futures.extend(submodule_tasks)
                        logger.info(f"Queued {len(submodule_tasks)} files from submodule {submodule_path}")
                    except Exception as e:
                        logger.error(f"Failed to process submodule {submodule_path}: {e}")
                        raise

            except Exception as e:
                logger.error(f"Error processing submodules: {e}")
                raise
        else:
            logger.info("No .gitmodules file found - repository has no submodules")

        # Collect all download results
        completed = 0
        total = len(futures)

        for future in as_completed(futures):
            try:
                file = future.result()
                if file is not None:  # Only append successfully downloaded files
                    all_files.append(file)
                completed += 1

                if completed % 100 == 0:
                    logger.info(f"Downloaded {completed}/{total} files...")

            except Exception as e:
                logger.warning(f"Download failed: {e}")
                # Cancel remaining tasks and re-raise
                for f in futures:
                    f.cancel()
                raise

    logger.info(f"Total files downloaded: {len(all_files)}")
    return all_files


def process_submodule_parallel(
    parent_repo: Repository,
    submodule_path: str,
    info: Dict[str, str],
    commit_hash: str,
    branch_name: str,
    user_id: str,
    server: str,
    repo_id: Optional[str],
    rate_limiter: GitHubRateLimiter,
    executor: ThreadPoolExecutor
) -> List[Any]:
    """
    Process a submodule and return download tasks for its files.
    """
    futures = []

    try:
        # Get submodule commit hash
        submodule_commit = get_submodule_commit_sha_with_retry(
            repo=parent_repo,
            submodule_path=submodule_path,
            commit_hash=commit_hash
        )

        if not submodule_commit:
            logger.warning(f"Couldn't determine commit for submodule {submodule_path}")
            return futures

        # Parse submodule URL
        submodule_url = info['url']
        https_url = ssh_to_https_url(submodule_url)
        submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

        if not submodule_owner or not submodule_repo_name:
            logger.error(f"Could not extract owner/repo from URL: {https_url}")
            return futures

        # Get access token and create GitHub client
        access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

        if domain and domain != 'github.com':
            g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
        else:
            g = Github(access_token)

        # Get submodule repository
        submodule_repo = get_submodule_repo(g, submodule_owner, submodule_repo_name)

        # Fetch submodule metadata ONCE
        submodule_metadata = RepoMetadata.from_repo(submodule_repo, submodule_commit)

        # List all files in submodule with cached metadata
        submodule_files_list = get_all_files_in_repo(
            submodule_repo,
            submodule_commit,
            submodule_paths=submodule_metadata.submodule_paths
        )

        # Create download tasks for submodule files
        for file_path, _ in submodule_files_list:
            task = DownloadTask(
                repo=submodule_repo,
                file_path=file_path,
                ref=submodule_commit,
                output_path=file_path,
                submodule_path=submodule_path,
                branch_name=branch_name,
                parent_repo_name=parent_repo.name,
                repo_metadata=submodule_metadata  # Pass cached metadata
            )
            future = executor.submit(download_file_with_rate_limit, task, rate_limiter)
            futures.append(future)

    except Exception as e:
        logger.error(f"Failed to process submodule {submodule_path}: {e}")
        raise

    return futures


@blitzy_exponential_retry()
def get_head_commit_hash(repo_name: str, user_id: str, server: str,
                         branch_name: str = "main", repo_id: Optional[str] = None) -> str:
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id)

    # Get the head commit hash
    branch = github_repo.get_branch(branch_name)
    commit_hash = branch.commit.sha
    logger.info(f"Head commit hash for branch {branch_name}: {commit_hash}")
    return commit_hash


@blitzy_exponential_retry()
def get_git_commit(sha: str, repo: Repository):
    return repo.get_git_commit(sha)


@blitzy_exponential_retry()
def get_commit(sha: str, repo: Repository):
    return repo.get_commit(sha)


@blitzy_exponential_retry()
def compare_git_sha(base: str, head: str, repo: Repository):
    return repo.compare(base=base, head=head)


@blitzy_exponential_retry()
def get_changed_files_between_commits(
    repo_name: str,
    user_id: str,
    server: str,
    base_commit: str,
    head_commit: str,
    repo_id: Optional[str] = None,
) -> List[str]:
    """
    Get all files changed between two commits in a GitHub repository, including changes in submodules.

    Args:
        repo_name: Name of the repository
        user_id: User ID for authentication
        server: Server URL for authentication
        base_commit: Base commit hash to compare from
        head_commit: Head commit hash to compare to
        include_submodule_changes: Whether to include files changed in submodules

    Returns:
        List of file paths that were changed between the two commits
    """
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id)

    try:
        # Get comparison between the two commits
        comparison = compare_git_sha(base=base_commit, head=head_commit, repo=github_repo)

        # Initialize a set to store unique file paths
        changed_files_set = set()

        for commit in comparison.commits:
            full_commit = get_commit(sha=commit.sha, repo=github_repo)
            for file in full_commit.files:
                changed_files_set.add(file.filename)

        # Parse .gitmodules to identify submodules
        try:
            gitmodules_text = get_gitmodules_content(repo=github_repo, ref=head_commit)
            submodules = parse_gitmodules(gitmodules_text)
            logger.info(f"Found {len(submodules)} submodules in repository")

            # Check which submodules have changed
            changed_submodules = []
            for file_path in changed_files_set:
                for submodule_path in submodules:
                    if file_path == submodule_path:
                        changed_submodules.append(submodule_path)
                        break

            # Process each changed submodule
            for submodule_path in changed_submodules:
                info = submodules[submodule_path]
                logger.info(f"Processing changes in submodule: {submodule_path}")

                # Get the submodule commit hash at the base commit
                old_sha = get_submodule_commit_sha(github_repo, submodule_path, base_commit)

                # Get the submodule commit hash at the head commit
                new_sha = get_submodule_commit_sha(github_repo, submodule_path, head_commit)

                # Skip if we don't have both SHAs or they're the same
                if not new_sha or old_sha == new_sha:
                    logger.info(f"Skipping submodule {submodule_path} - no changes detected")
                    continue

                # Get the submodule repository
                submodule_url = info['url']
                https_url = ssh_to_https_url(submodule_url)
                submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

                if not submodule_owner or not submodule_repo_name:
                    logger.error(f"Could not extract owner and repo name from URL: {https_url}")
                    continue

                # Get access to the submodule repository
                access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

                # Create appropriate Github object based on domain
                if domain and domain != 'github.com':
                    # GitHub Enterprise
                    g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
                else:
                    # Public GitHub
                    g = Github(access_token)

                try:
                    submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")

                    # Handle case where old_sha doesn't exist (newly added submodule)
                    if not old_sha:
                        logger.info(
                            f"Submodule {submodule_path} appears to be newly added - getting all files at {new_sha}")

                        # Get all files at the new commit hash
                        submodule_files = get_files_in_submodule(
                            submodule_repo=submodule_repo,
                            commit_hash=new_sha
                        )

                        # Add all files with the submodule path prefix
                        for file_path in submodule_files:
                            full_path = f"{submodule_path}/{file_path}"
                            changed_files_set.add(full_path)
                    else:
                        # Original comparison logic when we have both old and new SHA
                        submodule_comparison = compare_git_sha(base=old_sha, head=new_sha, repo=submodule_repo)

                        # Process each commit in the submodule to properly handle pagination
                        for submodule_commit in submodule_comparison.commits:
                            full_submodule_commit = get_commit(sha=submodule_commit.sha, repo=submodule_repo)

                            for submodule_file in full_submodule_commit.files:
                                # Add the file with the submodule path prefix
                                full_path = f"{submodule_path}/{submodule_file.filename}"
                                changed_files_set.add(full_path)

                    # Remove the submodule itself from the list since we're adding its contents
                    changed_files_set.discard(submodule_path)

                except GithubException as e:
                    logger.error(f"Error processing submodule {submodule_path}: {e}")

        except GithubException as e:
            logger.warning(f"Could not find .gitmodules file: {e}")

        # Convert the set to a list
        changed_files = list(changed_files_set)

        logger.info(f"Found {len(changed_files)} changed files between commits {base_commit[:7]} and {head_commit[:7]}")
        return changed_files

    except GithubException as e:
        logger.error(f"Error comparing commits {base_commit} and {head_commit}: {e}")
        return []


@blitzy_exponential_retry()
def get_submodule_commit_sha(repo: Repository, submodule_path: str, commit_hash: str) -> Optional[str]:
    """
    Get the commit SHA that a submodule is pointing to at a specific commit in the parent repository.
    """
    try:
        commit_obj = get_git_commit(sha=commit_hash, repo=repo)
        tree = repo.get_git_tree(commit_obj.tree.sha, recursive=True)

        for item in tree.tree:
            if item.path == submodule_path and item.type == 'commit':
                logger.info(f"Found submodule {submodule_path} with SHA {item.sha} in tree")
                return item.sha
    except GithubException as e:
        logger.warning(f"Could not get submodule from Git tree: {e}")
        return None


def get_files_in_submodule(submodule_repo: Repository, commit_hash: str) -> List[str]:
    """Get all file paths in a submodule repository at a specific commit."""
    file_paths = []

    def get_contents_recursively(path=""):
        try:
            contents = get_contents_with_retry(repo=submodule_repo, path=path, ref=commit_hash)

            # Handle single file
            if not isinstance(contents, list):
                contents = [contents]

            for content in contents:
                if content.type == "dir":
                    get_contents_recursively(content.path)
                elif content.type == "file":
                    file_paths.append(content.path)
        except GithubException as e:
            logger.error(f"Error getting contents at {path}, commit {commit_hash}: {e}")

    get_contents_recursively()
    return file_paths


@blitzy_exponential_retry()
def create_github_commit(
    repo: Repository,
    branch_name: str,
    base_branch: str,
    file_path: str,
    head_commit_hash: str,
    content="",
    create_new_branch=True,
    delete_file=False,
    is_new_repo=False,
    user_id=None,
    server=None,
    commit_message=None
) -> GitCommit | None | bool:
    """Create a commit for a file change with submodule support."""
    logger.info(f"Creating GitHub commit for file: {file_path}")

    # 1. Check if this file is in a submodule
    submodule_repo = None
    relative_path = None
    submodule_path = None

    # TODO: user_id will be gone
    if user_id and server and not is_new_repo:
        # This is the key check to properly detect submodules
        submodule_repo, relative_path, submodule_path = get_submodule_for_file(
            repo=repo,
            user_id=user_id,
            server=server,
            file_path=file_path,
            head_commit_hash=head_commit_hash
        )

    # 2. If file is in a submodule, handle it with the submodule-specific logic
    if submodule_repo:
        logger.info(f"File {file_path} is in submodule {submodule_path}, handling through submodule workflow")
        return commit_to_submodule(
            main_repo=repo,
            branch_name=branch_name,
            base_branch=base_branch,
            submodule_repo=submodule_repo,
            submodule_path=submodule_path,
            relative_path=relative_path,
            content=content,
            create_new_branch=create_new_branch,
            delete_file=delete_file,
            commit_message=commit_message
        )

    # 3. Regular (non-submodule) file handling continues below
    logger.info(f"File {file_path} is not in a submodule, proceeding with normal commit")

    # Regular file handling (not in submodule)
    # Use repo's default branch if base_branch is not provided
    if not base_branch or is_new_repo:
        base_branch = repo.default_branch

    # If branch_name and base_branch are the same, ensure we're using the correct reference
    if branch_name == base_branch:
        raise Exception("Base branch cannot be same as target branch")

    # Handle base branch
    base_branch_ref = None
    try:
        base_branch_ref = repo.get_git_ref(f"heads/{base_branch}")
        logger.info(f"Base branch '{base_branch}' exists")
    except GithubException as e:
        if e.status == 404:
            if is_new_repo:
                logger.info(f"Creating base branch '{base_branch}' with initial README.md")
                repo.create_file(
                    path="README.md",
                    message="Initial commit with README.md",
                    content=f"Repository created by Blitzy",
                    branch=base_branch
                )
                base_branch_ref = repo.get_git_ref(f"heads/{base_branch}")
            else:
                logger.error(f"Base branch '{base_branch}' does not exist and is_new_repo is False")
                raise
        else:
            raise

    # Handle target branch
    branch_ref = None
    try:
        branch_ref = repo.get_git_ref(f"heads/{branch_name}")
        logger.info(f"Branch '{branch_name}' already exists")
    except GithubException as e:
        if e.status == 404:
            if create_new_branch:
                logger.info(f"Creating new branch '{branch_name}' from '{base_branch}'")
                branch_ref = repo.create_git_ref(
                    ref=f"refs/heads/{branch_name}",
                    sha=base_branch_ref.object.sha
                )
            else:
                logger.error(f"Branch '{branch_name}' does not exist and create_new_branch is False")
                raise
        else:
            raise

    # Perform the requested file operation
    if delete_file:
        return commit_delete_file(
            repo=repo,
            branch_name=branch_name,
            branch_ref=branch_ref,
            file_path=file_path,
            commit_message=commit_message
        )
    else:
        return commit_create_or_update_file(
            repo=repo,
            branch_ref=branch_ref,
            file_path=file_path,
            content=content,
            commit_message=commit_message
        )


@blitzy_exponential_retry()
def get_submodule_for_file(repo: Repository, user_id: str, server: str,
                           file_path: str, head_commit_hash: str) \
        -> Tuple[Optional[Repository], Optional[str], Optional[str]]:
    try:
        # Get .gitmodules file content
        try:
            gitmodules_text = get_gitmodules_content(repo=repo, ref=head_commit_hash)
        except GithubException as gm_error:
            logger.warning(f"Could not find .gitmodules file: {gm_error}")
            return None, None, None

        # Parse .gitmodules file
        submodules = parse_gitmodules(gitmodules_text)

        # Find the submodule containing the file path
        matching_submodule_path = None
        for submodule_path in submodules:
            if file_path.startswith(submodule_path + '/'):
                matching_submodule_path = submodule_path
                break

        if matching_submodule_path:
            submodule_info = submodules[matching_submodule_path]
            submodule_url = submodule_info['url']

            # Extract relative path within submodule
            relative_path = file_path[len(matching_submodule_path) + 1:]

            # Convert SSH URL to HTTPS if needed
            https_url = ssh_to_https_url(submodule_url)

            # Extract owner, repo name, and domain
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

            if not submodule_owner or not submodule_repo_name:
                logger.error(f"Could not extract owner and repo name from URL: {https_url}")
                return None

            # Get the submodule repository
            access_token, _ = _get_token_and_installation_id(server, user_id, str(repo.id))

            # Create appropriate Github object based on domain
            if domain and domain != 'github.com':
                # GitHub Enterprise
                g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
            else:
                # Public GitHub
                g = Github(access_token)

            try:
                submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")
                return submodule_repo, relative_path, matching_submodule_path
            except GithubException as repo_error:
                logger.error(f"Could not access submodule repository: {repo_error}")
                return None, relative_path, matching_submodule_path
        else:
            logger.error(f"File {file_path} does not match any submodule path")
            return None, None, None

    except Exception as submodule_error:
        logger.error(f"Error processing submodules: {submodule_error}")
        return None, None, None


@blitzy_exponential_retry()
def commit_delete_file(
    repo: Repository,
    branch_name: str,
    branch_ref: GitRef,
    file_path: str,
    commit_message=None
):
    """Helper function to delete a file and create a commit."""
    try:
        # Get the file content first to get its sha
        file_content = get_contents_with_retry(repo=repo, path=file_path, ref=branch_name)

        # Delete the file
        message = commit_message or f"Delete file: {file_path}"
        repo.delete_file(
            path=file_path,
            message=message,
            sha=file_content.sha,
            branch=branch_name
        )

        return True

    except GithubException as e:
        if e.status == 404:
            # File doesn't exist, nothing to delete
            logger.warning(f"File {file_path} does not exist, nothing to delete")
            return None
        else:
            # For other exceptions, re-raise
            logger.error(f"Error deleting GitHub file {file_path}: {str(e)}")
            raise


@blitzy_exponential_retry()
def commit_create_or_update_file(
    repo: Repository,
    branch_ref: GitRef,
    file_path: str,
    content: str,
    commit_message=None
):
    """Helper function to create or update a file and create a commit."""
    try:
        # Create blob
        blob = repo.create_git_blob(content, "utf-8")

        # Get the latest commit on the branch
        base_tree = repo.get_git_tree(branch_ref.object.sha)

        # Create tree
        element = InputGitTreeElement(
            path=file_path,
            mode='100644',
            type='blob',
            sha=blob.sha
        )
        tree = repo.create_git_tree([element], base_tree)

        # Create commit
        parent = get_git_commit(sha=branch_ref.object.sha, repo=repo)
        message = commit_message or f"File: {file_path}"

        commit = repo.create_git_commit(
            message,
            tree,
            [parent]
        )

        # Update branch reference to point to the new commit
        branch_ref.edit(commit.sha, force=True)

        return commit
    except Exception as e:
        logger.error(f"Error creating GitHub commit for {file_path}: {str(e)}")
        raise


@blitzy_exponential_retry()
def commit_to_submodule(
    main_repo: Repository,
    branch_name: str,
    base_branch: str,
    submodule_repo: Repository,
    submodule_path: str,
    relative_path: str,
    content="",
    create_new_branch=True,
    delete_file=False,
    commit_message=None
) -> GitCommit:
    """
    Handle committing changes to files within submodules.

    This will:
    1. Commit the change to the submodule
    2. Update the submodule reference in the main repository
    """
    logger.info(f"Handling change to file {relative_path} in submodule {submodule_path}")

    # Get current submodule commit hash from main repo
    try:
        submodule_entry = get_contents_with_retry(repo=main_repo, path=submodule_path, ref=branch_name)
        current_submodule_commit = submodule_entry.sha
        logger.info(f"Current submodule commit is {current_submodule_commit}")
    except GithubException as e:
        logger.error(f"Error getting submodule reference: {e}")
        raise

    try:
        # Get submodule default branch
        submodule_default_branch = submodule_repo.default_branch

        # Try to get existing branch or create a new one
        try:
            submodule_branch_ref = submodule_repo.get_git_ref(f"heads/{branch_name}")
            logger.info(f"Branch {branch_name} already exists in submodule")
        except GithubException as e:
            if e.status == 404 and create_new_branch:
                # Get the default branch ref
                submodule_default_ref = submodule_repo.get_git_ref(f"heads/{submodule_default_branch}")

                # Create new branch in submodule
                submodule_branch_ref = submodule_repo.create_git_ref(
                    ref=f"refs/heads/{branch_name}",
                    sha=submodule_default_ref.object.sha
                )
                logger.info(f"Created branch {branch_name} in submodule")
            else:
                raise

        # Commit the change to the submodule
        if delete_file:
            submodule_commit = commit_delete_file(
                repo=submodule_repo,
                branch_name=branch_name,
                branch_ref=submodule_branch_ref,
                file_path=relative_path,
                commit_message=commit_message
            )
        else:
            submodule_commit = commit_create_or_update_file(
                repo=submodule_repo,
                branch_ref=submodule_branch_ref,
                file_path=relative_path,
                content=content,
                commit_message=commit_message
            )

        if not submodule_commit:
            logger.error("Failed to create commit in submodule")
            raise Exception("Failed to create commit in submodule")

        # Now update the submodule reference in the main repo
        # First, ensure the branch exists in the main repo
        main_branch_ref = None
        try:
            main_branch_ref = main_repo.get_git_ref(f"heads/{branch_name}")
            logger.info(f"Branch {branch_name} exists in main repo")
        except GithubException as e:
            if e.status == 404 and create_new_branch:
                # Get the base branch ref
                main_base_ref = main_repo.get_git_ref(f"heads/{base_branch or main_repo.default_branch}")

                # Create new branch
                main_branch_ref = main_repo.create_git_ref(
                    ref=f"refs/heads/{branch_name}",
                    sha=main_base_ref.object.sha
                )
                logger.info(f"Created branch {branch_name} in main repo")
            else:
                raise

        # Update the submodule in the main repo to point to the new commit
        main_tree = main_repo.get_git_tree(main_branch_ref.object.sha)

        # Create a new tree with the updated submodule
        new_element = InputGitTreeElement(
            path=submodule_path,
            mode='160000',  # Special mode for submodules
            type='commit',
            sha=submodule_commit.sha
        )

        new_tree = main_repo.create_git_tree([new_element], main_tree)

        # Create the commit in the main repo
        parent = get_git_commit(sha=main_branch_ref.object.sha, repo=main_repo)
        main_commit_message = commit_message or f"Update submodule {submodule_path} to include changes to {relative_path}"

        main_commit = main_repo.create_git_commit(
            message=main_commit_message,
            tree=new_tree,
            parents=[parent]
        )

        # Update the branch reference
        main_branch_ref.edit(main_commit.sha, force=True)

        logger.info(f"Updated submodule reference in main repo, commit {main_commit.sha}")

        return main_commit

    except Exception as e:
        logger.error(f"Error updating submodule: {e}")
        raise


@blitzy_exponential_retry()
def create_all_pull_requests(
    repo: Union[Repository, str],
    head_branch: str,
    user_id: str,
    server: str,
    base_branch="",
    pr_title=None,
    pr_body=None,
    is_new_repo=False,
    git_project_repo_id: Optional[str] = None,
) -> List[Union[PullRequest, Dict]]:
    """
    Create pull requests for the specified branch and optionally for matching branches in submodules.
    Supports both GitHub and Azure DevOps repositories.

    Args:
        repo: The GitHub repository object or repo name (for Azure DevOps)
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR (defaults to repo's default branch)
        user_id: User ID for authentication (needed for submodule access)
        server: Server URL for authentication (needed for submodule access)
        pr_title: Custom PR title (defaults to "Autonomous changes created by Blitzy")
        pr_body: Custom PR body (defaults to standard message)
        is_new_repo: Whether this is a new repository
        git_project_repo_id: Repository ID for service type detection and Azure DevOps

    Returns:
        List of created/existing pull request objects (GitHub) or dictionaries (Azure DevOps)
    """
    # Determine service type
    if git_project_repo_id:
        svc_type = _get_service_type(user_id, git_project_repo_id)
        if svc_type == SvcType.AZURE_DEVOPS:
            # Use Azure DevOps implementation
            from blitzy_utils.azure_utils import create_all_pull_requests_azure_devops

            return create_all_pull_requests_azure_devops(
                git_project_repo_id=git_project_repo_id,
                head_branch=head_branch,
                base_branch=base_branch,
                pr_title=pr_title,
                pr_body=pr_body,
                is_new_repo=is_new_repo,
            )

    # GitHub implementation (default)
    if not isinstance(repo, Repository):
        raise ValueError("For GitHub repositories, repo must be a Repository object")

    if not base_branch or is_new_repo:
        base_branch = repo.default_branch

    created_prs = []

    # Create PR for main repository
    main_pr = create_single_pull_request(
        repo=repo,
        head_branch=head_branch,
        base_branch=base_branch,
        pr_title=pr_title or "Autonomous changes created by Blitzy",
        pr_body=pr_body or "This PR contains automated updates created by Blitzy"
    )
    created_prs.append(main_pr)

    # Check for submodules with matching branch
    try:
        # Get .gitmodules file content
        try:
            gitmodules_text = get_gitmodules_content(repo=repo, ref=base_branch)
        except GithubException as e:
            logger.warning(f"No .gitmodules file found assuming no submodules: {e}")
            return created_prs

        # Parse .gitmodules file
        submodules = parse_gitmodules(gitmodules_text)

        # For each submodule, check if the branch exists and create PR if it does
        for submodule_path, submodule_info in submodules.items():
            submodule_url = submodule_info['url']

            # Convert SSH URL to HTTPS if needed
            https_url = ssh_to_https_url(submodule_url)

            # Extract owner, repo name, and domain
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

            if not submodule_owner or not submodule_repo_name:
                logger.error(f"Could not extract owner and repo name from URL: {https_url}")
                continue

            # Get the submodule repository
            access_token, _ = _get_token_and_installation_id(server, user_id, str(repo.id))

            # Create appropriate Github object based on domain
            if domain and domain != 'github.com':
                # GitHub Enterprise
                g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
            else:
                # Public GitHub
                g = Github(access_token)

            try:
                submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")

                # Check if the same branch exists in the submodule
                try:
                    submodule_repo.get_branch(head_branch)
                    logger.info(f"Found matching branch '{head_branch}' in submodule {submodule_path}")

                    # Create PR for the submodule
                    submodule_pr_title = f"Submodule update: {pr_title or 'Autonomous changes created by Blitzy'}"
                    submodule_pr_body = f"This PR is part of changes in the parent repository.\n\n{pr_body or 'This PR contains automated updates created by Blitzy'}"

                    if main_pr:
                        submodule_pr_body += f"\n\nRelated to parent repository PR: {main_pr.html_url}"

                    submodule_pr = create_single_pull_request(
                        repo=submodule_repo,
                        head_branch=head_branch,
                        base_branch=submodule_repo.default_branch,  # Use the submodule's default branch
                        pr_title=submodule_pr_title,
                        pr_body=submodule_pr_body
                    )
                    created_prs.append(submodule_pr)

                    new_body = main_pr.body + f"\n\nSubmodule PR created: {submodule_pr.html_url}"
                    main_pr.edit(body=new_body)

                except GithubException as branch_error:
                    if branch_error.status == 404:
                        logger.info(f"Branch '{head_branch}' does not exist in submodule {submodule_path}")
                    else:
                        logger.error(f"Error checking branch in submodule {submodule_path}: {branch_error}")

            except GithubException as repo_error:
                logger.error(f"Could not access submodule repository: {repo_error}")

    except Exception as e:
        logger.error(f"Error processing submodules for PR: {e}")

    return created_prs


@blitzy_exponential_retry()
def create_single_pull_request(
    repo: Union[Repository, str],
    head_branch: str,
    base_branch: str,
    pr_title: str,
    pr_body: str,
    user_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> Optional[Union[PullRequest, Dict]]:
    """
    Create a single pull request in the specified repository.
    Supports both GitHub and Azure DevOps repositories.

    Args:
        repo: GitHub Repository object or repo name (for Azure DevOps)
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR
        pr_title: Title for the pull request
        pr_body: Description for the pull request
        user_id: User ID for service type detection (Azure DevOps)
        git_project_repo_id: Repository ID for service type detection and Azure DevOps

    Returns:
        GitHub PullRequest object or Azure DevOps pull request dictionary
    """
    # Determine service type for Azure DevOps
    if git_project_repo_id and user_id:
        svc_type = _get_service_type(user_id, git_project_repo_id)
        if svc_type == SvcType.AZURE_DEVOPS:
            # Use Azure DevOps implementation
            from blitzy_utils.azure_utils import (
                create_single_pull_request_azure_devops,
                _get_azure_devops_credentials_by_repo_id,
            )

            git_project_repo = _get_azure_devops_credentials_by_repo_id(
                git_project_repo_id
            )
            return create_single_pull_request_azure_devops(
                organization=git_project_repo.azure_org_name,
                project_id=git_project_repo.azure_project_id,
                repo_id=git_project_repo.repo_id,
                access_token=git_project_repo.access_token,
                head_branch=head_branch,
                base_branch=base_branch,
                pr_title=pr_title,
                pr_body=pr_body,
            )

    # GitHub implementation (default)
    if not isinstance(repo, Repository):
        raise ValueError("For GitHub repositories, repo must be a Repository object")

    try:
        # Check if PR already exists
        existing_prs = repo.get_pulls(
            state='open',
            head=f"{repo.owner.login}:{head_branch}",
            base=base_branch
        )

        # Use existing PR if it exists
        for pr in existing_prs:
            logger.info(f"Using existing PR #{pr.number}: {pr.html_url}")
            return pr

        # Create new PR if none exists
        pr = repo.create_pull(
            title=pr_title,
            body=pr_body,
            head=head_branch,
            base=base_branch
        )

        logger.info(f"Created new PR #{pr.number}: {pr.html_url}")
        return pr

    except Exception as e:
        logger.error(f"Error handling pull request: {str(e)}")
        return None


@blitzy_exponential_retry()
def setup_github_branch(
        repo: Repository,
        branch_name: str,
        base_branch="",
        create_new_branch=True,
        delete_existing_branch=True
) -> str:
    if not base_branch:
        base_branch = repo.default_branch

    try:
        # Check if repo is empty by attempting to get the default branch
        is_empty_repo = False
        try:
            repo.get_git_ref(f"heads/{base_branch}")
        except GithubException as e:
            if e.status == 409:
                logger.warning("Cannot create branch for an empty repository")
                is_empty_repo = True
            else:
                # If it's another GitHub exception, re-raise it
                raise

        if not is_empty_repo:
            branch_exists = True
            try:
                # Try to get the branch reference
                branch_ref = repo.get_git_ref(f"heads/{branch_name}")

                # If branch exists and delete_existing_branch is True, delete it
                if delete_existing_branch:
                    logger.info(f'Deleting existing branch {branch_name}')
                    branch_ref.delete()
                    branch_exists = False
                    # We'll need to create a new branch
                else:
                    # Return existing branch if we're not deleting it
                    return branch_ref

            except GithubException as e:
                # Only treat 404 as branch doesn't exist
                if e.status == 404:
                    logger.warning(f'Branch {branch_name} does not exist')
                    branch_exists = False
                else:
                    # For any other GitHub exception, re-raise it
                    raise

            # Create a new branch if needed (branch doesn't exist or was deleted)
            if (not branch_exists) and create_new_branch:
                # Get the base branch reference
                base_ref = repo.get_git_ref(f"heads/{base_branch}")

                # Create new branch from default branch
                branch_ref = repo.create_git_ref(
                    ref=f"refs/heads/{branch_name}",
                    sha=base_ref.object.sha
                )
                logger.info(f'Created new branch {branch_name}')
                return branch_ref
            elif not create_new_branch and not branch_exists:
                raise Exception(f"Branch {branch_name} does not exist and create_new_branch is False")

            return branch_ref
        else:
            return None

    except Exception as e:
        logger.error(f"Error creating GitHub branch {branch_name}: {str(e)}")
        raise


def get_repo_info_by_id(repo_id: str):
    """
    This function fetches repository metadata which is part of `github_project_repo` table.
    Sample response looks like this:
    {
        "installationType": "ORGANIZATION",
        "orgId": "16291886",
        "orgName": "blitzy-ai",
        "repoId": "1011128959",
        "repoName": "blitzy-utils-python",
        "svcType": null
    }
    :param repo_id: ID of the repository.
    :return: Github metadata.
    """
    with ServiceClient() as client:
        response = client.get("github", f"v1/github/repositories/{repo_id}")
        if response.status_code > 299:
            logger.warning(
                f"Failed to get repo metadata using github handler. "
                f"Status code: {response.status_code}, payload {response.text}"
            )
        response.raise_for_status()
        return response.json()
