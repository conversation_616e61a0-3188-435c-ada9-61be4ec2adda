"""
Azure DevOps Pull Request Integration Test Suite

Integration tests for Azure DevOps pull request operations in blitzy_utils,
following the testing patterns from archie-github-handler.
"""

import logging
import time
import sys
import os
from contextlib import contextmanager
from typing import Callable, List, Optional, Dict, Any

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

try:
    from logger import logger
except ImportError:
    # Fallback to standard logging if blitzy logger not available
    logger = logging.getLogger(__name__)

# Test Configuration - Update these with real Azure DevOps test environment values
TEST_CONFIG = {
    'organization': 'azureblitzy1',
    'project_id': '92381f74-980b-4868-a402-90a78cb346ec',
    'repo_id': 'e2e0696f-9d4c-4f3d-aefc-dfa3de642276',
    'access_token': '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',  # Will be populated from environment or config
    'test_branch': 'test-pr-branch',
    'base_branch': 'main',
    'git_project_repo_id': 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    'user_id': 'eba17c5f-a04f-4e79-b512-7cec8a406e15'
}


class TestRunner:
    """Test runner with pass/fail tracking and logging control."""

    def __init__(self, suppress_logging: bool = True):
        self.suppress_logging = suppress_logging
        self.results = {
            'passed': 0,
            'failed': 0,
            'total': 0,
            'details': []
        }

    @contextmanager
    def logging_suppressed(self):
        """Context manager to suppress logging during tests."""
        if self.suppress_logging:
            # Store original log levels
            root_logger = logging.getLogger()
            blitzy_logger = logging.getLogger('blitzy')
            original_root_level = root_logger.level
            original_blitzy_level = blitzy_logger.level

            # Suppress logging
            root_logger.setLevel(logging.CRITICAL + 1)
            blitzy_logger.setLevel(logging.CRITICAL + 1)

            try:
                yield
            finally:
                # Restore original log levels
                root_logger.setLevel(original_root_level)
                blitzy_logger.setLevel(original_blitzy_level)
        else:
            yield

    def run_test(self, test_func: Callable, test_name: str | None = None) -> bool:
        """Run a single test and track results."""
        if test_name is None:
            test_name = test_func.__name__

        self.results['total'] += 1

        try:
            with self.logging_suppressed():
                test_func()

            self.results['passed'] += 1
            self.results['details'].append({
                'name': test_name,
                'status': 'PASSED',
                'error': None
            })
            print(f"✅ {test_name}: PASSED")
            return True

        except Exception as e:
            self.results['failed'] += 1
            self.results['details'].append({
                'name': test_name,
                'status': 'FAILED',
                'error': str(e)
            })
            print(f"❌ {test_name}: FAILED - {str(e)}")
            return False

    def print_summary(self):
        """Print test execution summary."""
        print("\n" + "="*60)
        print("AZURE DEVOPS PULL REQUEST INTEGRATION TEST SUMMARY")
        print("="*60)
        print(f"Total Tests: {self.results['total']}")
        print(f"Passed: {self.results['passed']} ✅")
        print(f"Failed: {self.results['failed']} ❌")

        if self.results['total'] > 0:
            pass_rate = (self.results['passed'] / self.results['total']) * 100
            print(f"Pass Rate: {pass_rate:.1f}%")

        if self.results['failed'] > 0:
            print("\nFAILED TESTS:")
            for detail in self.results['details']:
                if detail['status'] == 'FAILED':
                    print(f"  • {detail['name']}: {detail['error']}")

        print("="*60)


def setup_test_environment():
    """Setup test environment and validate configuration."""
    # TODO: Add logic to fetch access token from environment or config
    # For now, we'll assume it's set in TEST_CONFIG
    if not TEST_CONFIG['access_token']:
        # Try to get from environment variable
        import os
        TEST_CONFIG['access_token'] = os.getenv('AZURE_DEVOPS_ACCESS_TOKEN', '')
    
    if not TEST_CONFIG['access_token']:
        raise ValueError("Azure DevOps access token not configured. Set AZURE_DEVOPS_ACCESS_TOKEN environment variable.")


def test_create_single_pull_request_azure_devops():
    """Test creating a single pull request in Azure DevOps."""
    from blitzy_utils.azure_utils import create_single_pull_request_azure_devops

    # Generate unique PR title to avoid conflicts
    timestamp = int(time.time())
    pr_title = f"Integration Test PR - {timestamp}"
    pr_body = f"This is an integration test pull request created at {time.ctime()}"

    try:
        result = create_single_pull_request_azure_devops(
            organization=TEST_CONFIG['organization'],
            project_id=TEST_CONFIG['project_id'],
            repo_id=TEST_CONFIG['repo_id'],
            access_token=TEST_CONFIG['access_token'],
            head_branch=TEST_CONFIG['test_branch'],
            base_branch=TEST_CONFIG['base_branch'],
            pr_title=pr_title,
            pr_body=pr_body
        )

        # Verify result structure
        assert result is not None, "No result returned from create_single_pull_request_azure_devops"
        assert isinstance(result, dict), f"Expected dict, got {type(result)}"
        assert 'id' in result, "Result missing 'id' field"
        assert 'title' in result, "Result missing 'title' field"
        assert 'url' in result, "Result missing 'url' field"
        assert 'status' in result, "Result missing 'status' field"

        # Verify content
        assert result['title'] == pr_title, f"Title mismatch: {result['title']} != {pr_title}"
        assert result['status'] == 'active', f"Expected status 'active', got {result['status']}"
        assert isinstance(result['id'], int), f"Expected int ID, got {type(result['id'])}"

        logger.info(f"Successfully created PR #{result['id']}: {result['url']}")

    except Exception as e:
        error_msg = str(e).lower()
        if "already exists" in error_msg or "duplicate" in error_msg:
            # PR already exists - this is acceptable for integration tests
            logger.info("Pull request already exists (acceptable for integration test)")
            return
        else:
            raise  # Re-raise other errors


def test_create_single_pull_request_duplicate_detection():
    """Test that duplicate pull request detection works correctly."""
    from blitzy_utils.azure_utils import create_single_pull_request_azure_devops

    pr_title = "Duplicate Detection Test PR"
    pr_body = "Testing duplicate detection functionality"

    # Create first PR
    result1 = create_single_pull_request_azure_devops(
        organization=TEST_CONFIG['organization'],
        project_id=TEST_CONFIG['project_id'],
        repo_id=TEST_CONFIG['repo_id'],
        access_token=TEST_CONFIG['access_token'],
        head_branch=TEST_CONFIG['test_branch'],
        base_branch=TEST_CONFIG['base_branch'],
        pr_title=pr_title,
        pr_body=pr_body
    )

    # Create second PR with same parameters - should return existing PR
    result2 = create_single_pull_request_azure_devops(
        organization=TEST_CONFIG['organization'],
        project_id=TEST_CONFIG['project_id'],
        repo_id=TEST_CONFIG['repo_id'],
        access_token=TEST_CONFIG['access_token'],
        head_branch=TEST_CONFIG['test_branch'],
        base_branch=TEST_CONFIG['base_branch'],
        pr_title=pr_title,
        pr_body=pr_body
    )

    # Both results should be the same (existing PR)
    assert result1 is not None and result2 is not None, "One of the results is None"
    assert result1['id'] == result2['id'], f"PR IDs don't match: {result1['id']} != {result2['id']}"
    assert result1['url'] == result2['url'], f"PR URLs don't match: {result1['url']} != {result2['url']}"

    logger.info(f"Duplicate detection working correctly - reused PR #{result1['id']}")


def test_create_all_pull_requests_azure_devops():
    """Test creating pull requests for main repo and submodules."""
    from blitzy_utils.azure_utils import create_all_pull_requests_azure_devops

    timestamp = int(time.time())
    pr_title = f"Integration Test - All PRs - {timestamp}"
    pr_body = f"Testing create_all_pull_requests_azure_devops at {time.ctime()}"

    try:
        results = create_all_pull_requests_azure_devops(
            git_project_repo_id=TEST_CONFIG['git_project_repo_id'],
            head_branch=TEST_CONFIG['test_branch'],
            base_branch=TEST_CONFIG['base_branch'],
            pr_title=pr_title,
            pr_body=pr_body,
            is_new_repo=False
        )

        # Verify results structure
        assert results is not None, "No results returned from create_all_pull_requests_azure_devops"
        assert isinstance(results, list), f"Expected list, got {type(results)}"
        assert len(results) > 0, "No pull requests created"

        # Verify each PR in results
        for i, pr in enumerate(results):
            assert isinstance(pr, dict), f"PR {i} is not a dict: {type(pr)}"
            assert 'id' in pr, f"PR {i} missing 'id' field"
            assert 'title' in pr, f"PR {i} missing 'title' field"
            assert 'url' in pr, f"PR {i} missing 'url' field"

            logger.info(f"Created/Found PR #{pr['id']}: {pr['url']}")

        logger.info(f"Successfully processed {len(results)} pull request(s)")

    except Exception as e:
        error_msg = str(e).lower()
        if "repository" in error_msg and "not found" in error_msg:
            # Repository not found - acceptable for integration test
            logger.warning("Repository not found (acceptable for integration test)")
            return
        else:
            raise  # Re-raise other errors


def test_unified_create_single_pull_request():
    """Test the unified create_single_pull_request function with Azure DevOps detection."""
    from github import create_single_pull_request

    timestamp = int(time.time())
    pr_title = f"Unified Function Test - {timestamp}"
    pr_body = f"Testing unified create_single_pull_request with Azure DevOps at {time.ctime()}"

    try:
        result = create_single_pull_request(
            repo=TEST_CONFIG['repo_id'],  # Pass repo ID as string for Azure DevOps
            head_branch=TEST_CONFIG['test_branch'],
            base_branch=TEST_CONFIG['base_branch'],
            pr_title=pr_title,
            pr_body=pr_body,
            user_id=TEST_CONFIG['user_id'],
            git_project_repo_id=TEST_CONFIG['git_project_repo_id']
        )

        # Verify result structure (should be Azure DevOps format)
        assert result is not None, "No result returned from unified create_single_pull_request"
        assert isinstance(result, dict), f"Expected dict for Azure DevOps, got {type(result)}"
        assert 'id' in result, "Result missing 'id' field"
        assert 'title' in result, "Result missing 'title' field"
        assert 'url' in result, "Result missing 'url' field"

        logger.info(f"Unified function successfully created Azure DevOps PR #{result['id']}: {result['url']}")

    except Exception as e:
        error_msg = str(e).lower()
        if "service type" in error_msg or "credentials" in error_msg:
            # Service type detection or credential issues - log and continue
            logger.warning(f"Service detection issue (acceptable for integration test): {e}")
            return
        else:
            raise  # Re-raise other errors


def test_pull_request_parameter_validation():
    """Test parameter validation for Azure DevOps pull request functions."""
    from blitzy_utils.azure_utils import create_single_pull_request_azure_devops

    # Test empty organization
    try:
        create_single_pull_request_azure_devops(
            organization="",
            project_id=TEST_CONFIG['project_id'],
            repo_id=TEST_CONFIG['repo_id'],
            access_token=TEST_CONFIG['access_token'],
            head_branch=TEST_CONFIG['test_branch'],
            base_branch=TEST_CONFIG['base_branch'],
            pr_title="Test PR",
            pr_body="Test PR body"
        )
        raise AssertionError("Expected exception for empty organization")
    except Exception as e:
        # Accept any exception that indicates validation failure
        error_msg = str(e).lower()
        assert any(keyword in error_msg for keyword in ['organization', 'invalid', 'empty', 'required']), \
            f"Unexpected error message: {e}"

    # Test empty access token
    try:
        create_single_pull_request_azure_devops(
            organization=TEST_CONFIG['organization'],
            project_id=TEST_CONFIG['project_id'],
            repo_id=TEST_CONFIG['repo_id'],
            access_token="",
            head_branch=TEST_CONFIG['test_branch'],
            base_branch=TEST_CONFIG['base_branch'],
            pr_title="Test PR",
            pr_body="Test PR body"
        )
        raise AssertionError("Expected exception for empty access token")
    except Exception as e:
        # Accept any exception that indicates authentication failure
        error_msg = str(e).lower()
        assert any(keyword in error_msg for keyword in ['token', 'auth', 'credential', 'unauthorized']), \
            f"Unexpected error message: {e}"


def test_branch_reference_formatting():
    """Test that branch references are properly formatted with refs/ prefix."""
    from blitzy_utils.azure_utils import create_single_pull_request_azure_devops

    timestamp = int(time.time())
    pr_title = f"Branch Ref Test - {timestamp}"
    pr_body = "Testing branch reference formatting"

    # Test with branches that already have refs/ prefix
    try:
        result = create_single_pull_request_azure_devops(
            organization=TEST_CONFIG['organization'],
            project_id=TEST_CONFIG['project_id'],
            repo_id=TEST_CONFIG['repo_id'],
            access_token=TEST_CONFIG['access_token'],
            head_branch=f"refs/heads/{TEST_CONFIG['test_branch']}",
            base_branch=f"refs/heads/{TEST_CONFIG['base_branch']}",
            pr_title=pr_title,
            pr_body=pr_body
        )

        # Verify the PR was created/found successfully
        assert result is not None, "No result returned for refs/ formatted branches"
        assert 'source_ref' in result, "Result missing 'source_ref' field"
        assert 'target_ref' in result, "Result missing 'target_ref' field"

        # Verify refs are properly formatted
        assert result['source_ref'].startswith('refs/heads/'), f"Source ref not properly formatted: {result['source_ref']}"
        assert result['target_ref'].startswith('refs/heads/'), f"Target ref not properly formatted: {result['target_ref']}"

        logger.info(f"Branch reference formatting test passed - PR #{result['id']}")

    except Exception as e:
        error_msg = str(e).lower()
        if "branch" in error_msg and ("not found" in error_msg or "does not exist" in error_msg):
            # Branch doesn't exist - acceptable for integration test
            logger.warning("Test branch not found (acceptable for integration test)")
            return
        else:
            raise  # Re-raise other errors


def test_error_handling_and_retry():
    """Test error handling and retry mechanisms."""
    from blitzy_utils.azure_utils import create_single_pull_request_azure_devops

    # Test with invalid project ID to trigger error handling
    try:
        create_single_pull_request_azure_devops(
            organization=TEST_CONFIG['organization'],
            project_id="invalid-project-id",
            repo_id=TEST_CONFIG['repo_id'],
            access_token=TEST_CONFIG['access_token'],
            head_branch=TEST_CONFIG['test_branch'],
            base_branch=TEST_CONFIG['base_branch'],
            pr_title="Error Handling Test",
            pr_body="Testing error handling"
        )
        raise AssertionError("Expected exception for invalid project ID")
    except Exception as e:
        # Verify we get a meaningful error message
        error_msg = str(e).lower()
        assert any(keyword in error_msg for keyword in ['project', 'not found', 'invalid', 'does not exist']), \
            f"Unexpected error message: {e}"
        logger.info(f"Error handling test passed - got expected error: {e}")


def test_large_pr_content():
    """Test creating pull request with large title and body content."""
    from blitzy_utils.azure_utils import create_single_pull_request_azure_devops

    timestamp = int(time.time())

    # Create large content
    large_title = f"Large Content Test {timestamp} - " + "A" * 200  # Long title
    large_body = f"Large PR Body Test - {timestamp}\n\n" + "\n".join([
        f"Line {i}: " + "B" * 100 for i in range(50)  # 50 lines of content
    ])

    try:
        result = create_single_pull_request_azure_devops(
            organization=TEST_CONFIG['organization'],
            project_id=TEST_CONFIG['project_id'],
            repo_id=TEST_CONFIG['repo_id'],
            access_token=TEST_CONFIG['access_token'],
            head_branch=TEST_CONFIG['test_branch'],
            base_branch=TEST_CONFIG['base_branch'],
            pr_title=large_title,
            pr_body=large_body
        )

        # Verify the PR was created successfully
        assert result is not None, "No result returned for large content PR"
        assert 'id' in result, "Result missing 'id' field"
        assert 'title' in result, "Result missing 'title' field"

        # Verify content was preserved (may be truncated by Azure DevOps)
        assert len(result['title']) > 0, "Title is empty"

        logger.info(f"Large content test passed - PR #{result['id']} created with {len(large_title)} char title and {len(large_body)} char body")

    except Exception as e:
        error_msg = str(e).lower()
        if "too long" in error_msg or "exceeds" in error_msg or "limit" in error_msg:
            # Content too long - acceptable, Azure DevOps has limits
            logger.info("Large content rejected by Azure DevOps (acceptable - platform limits)")
            return
        elif "branch" in error_msg and "not found" in error_msg:
            # Branch doesn't exist - acceptable for integration test
            logger.warning("Test branch not found (acceptable for integration test)")
            return
        else:
            raise  # Re-raise other errors


def test_concurrent_pull_request_creation():
    """Test concurrent pull request creation to verify thread safety."""
    import threading
    from blitzy_utils.azure_utils import create_single_pull_request_azure_devops

    timestamp = int(time.time())
    results = []
    errors = []

    def create_pr_thread(thread_id):
        """Thread function to create a pull request."""
        try:
            result = create_single_pull_request_azure_devops(
                organization=TEST_CONFIG['organization'],
                project_id=TEST_CONFIG['project_id'],
                repo_id=TEST_CONFIG['repo_id'],
                access_token=TEST_CONFIG['access_token'],
                head_branch=TEST_CONFIG['test_branch'],
                base_branch=TEST_CONFIG['base_branch'],
                pr_title=f"Concurrent Test {timestamp} - Thread {thread_id}",
                pr_body=f"Testing concurrent creation from thread {thread_id}"
            )
            results.append(result)
        except Exception as e:
            errors.append(f"Thread {thread_id}: {e}")

    # Create multiple threads
    threads = []
    for i in range(3):  # Use 3 threads to avoid overwhelming the API
        thread = threading.Thread(target=create_pr_thread, args=(i,))
        threads.append(thread)
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

    # Verify results
    if errors:
        # Check if errors are acceptable (e.g., branch not found, duplicate PRs)
        acceptable_errors = all(
            any(keyword in str(error).lower() for keyword in ['branch', 'not found', 'duplicate', 'already exists'])
            for error in errors
        )
        if not acceptable_errors:
            raise AssertionError(f"Unexpected errors in concurrent test: {errors}")
        logger.warning(f"Concurrent test had acceptable errors: {errors}")

    if results:
        # Verify all results have the same structure
        for i, result in enumerate(results):
            assert isinstance(result, dict), f"Result {i} is not a dict"
            assert 'id' in result, f"Result {i} missing 'id' field"

        logger.info(f"Concurrent test passed - {len(results)} PRs created/found, {len(errors)} acceptable errors")
    else:
        logger.warning("No PRs created in concurrent test (acceptable if branches don't exist)")


def test_unified_create_all_pull_requests():
    """Test the unified create_all_pull_requests function with Azure DevOps detection."""
    from github import create_all_pull_requests

    timestamp = int(time.time())
    pr_title = f"Unified All PRs Test - {timestamp}"
    pr_body = f"Testing unified create_all_pull_requests with Azure DevOps at {time.ctime()}"

    try:
        results = create_all_pull_requests(
            repo=TEST_CONFIG['repo_id'],  # Pass repo ID as string for Azure DevOps
            head_branch=TEST_CONFIG['test_branch'],
            user_id=TEST_CONFIG['user_id'],
            server="azure_devops",  # Indicate Azure DevOps server
            base_branch=TEST_CONFIG['base_branch'],
            pr_title=pr_title,
            pr_body=pr_body,
            git_project_repo_id=TEST_CONFIG['git_project_repo_id']
        )

        # Verify results structure (should be Azure DevOps format)
        assert results is not None, "No results returned from unified create_all_pull_requests"
        assert isinstance(results, list), f"Expected list for Azure DevOps, got {type(results)}"

        if len(results) > 0:
            # Verify each PR in results
            for i, pr in enumerate(results):
                assert isinstance(pr, dict), f"PR {i} is not a dict: {type(pr)}"
                assert 'id' in pr, f"PR {i} missing 'id' field"
                assert 'title' in pr, f"PR {i} missing 'title' field"

        logger.info(f"Unified create_all_pull_requests test passed - {len(results)} PRs processed")

    except Exception as e:
        error_msg = str(e).lower()
        if any(keyword in error_msg for keyword in ['service type', 'credentials', 'repository', 'not found']):
            # Service detection or repository issues - log and continue
            logger.warning(f"Service/repository issue (acceptable for integration test): {e}")
            return
        else:
            raise  # Re-raise other errors


def run_all_integration_tests(suppress_logging: bool = True, run_specific: Optional[List[str]] = None):
    """
    Run all integration test functions with progress tracking.

    Args:
        suppress_logging: Whether to suppress logging during test execution
        run_specific: List of specific test names to run (None = run all)
    """
    runner = TestRunner(suppress_logging=suppress_logging)

    # Setup test environment
    try:
        setup_test_environment()
    except Exception as e:
        print(f"❌ Test environment setup failed: {e}")
        print("Please ensure AZURE_DEVOPS_ACCESS_TOKEN environment variable is set.")
        return {'passed': 0, 'failed': 1, 'total': 1, 'details': []}

    all_tests = [
        (test_create_single_pull_request_azure_devops, "Create Single Pull Request - Azure DevOps"),
        (test_create_single_pull_request_duplicate_detection, "Duplicate Pull Request Detection"),
        (test_create_all_pull_requests_azure_devops, "Create All Pull Requests - Azure DevOps"),
        (test_unified_create_single_pull_request, "Unified Create Single Pull Request"),
        (test_pull_request_parameter_validation, "Pull Request Parameter Validation"),
        (test_branch_reference_formatting, "Branch Reference Formatting"),
        (test_error_handling_and_retry, "Error Handling and Retry"),
        (test_large_pr_content, "Large PR Content Handling"),
        (test_concurrent_pull_request_creation, "Concurrent Pull Request Creation"),
        (test_unified_create_all_pull_requests, "Unified Create All Pull Requests"),
    ]

    # Filter tests if specific ones requested
    if run_specific:
        all_tests = [(func, name) for func, name in all_tests
                     if func.__name__ in run_specific or name in run_specific]

    print(f"Running {len(all_tests)} Azure DevOps pull request integration tests...")
    print("-" * 60)

    for test_func, test_name in all_tests:
        runner.run_test(test_func, test_name)

    runner.print_summary()
    return runner.results


if __name__ == "__main__":
    # Configuration options
    SUPPRESS_LOGGING = False  # Set to False to see all logs during tests

    # Run all integration tests
    run_all_integration_tests(suppress_logging=SUPPRESS_LOGGING)

    # Or run specific tests:
    # run_all_integration_tests(suppress_logging=SUPPRESS_LOGGING,
    #                          run_specific=['test_create_single_pull_request_azure_devops'])
