# Azure DevOps Pull Request Integration Tests

This document describes the integration tests for Azure DevOps pull request functionality implemented in blitzy-utils-python, following the testing patterns from archie-github-handler.

## Overview

The integration tests validate the Azure DevOps pull request creation and management functionality in real Azure DevOps environments. These tests complement the unit tests by verifying actual API interactions and end-to-end workflows.

## Test Structure

### Test Files

- **`test_azure_pull_requests_integration.py`** - Main integration test suite
- **`run_azure_pr_integration_tests.py`** - Test runner script
- **`test_azure_pull_requests.py`** - Unit tests (for comparison)

### Test Categories

1. **Core Functionality Tests**
   - `test_create_single_pull_request_azure_devops` - Basic PR creation
   - `test_create_all_pull_requests_azure_devops` - Bulk PR creation with submodules
   - `test_create_single_pull_request_duplicate_detection` - Duplicate PR handling

2. **Unified Interface Tests**
   - `test_unified_create_single_pull_request` - GitHub/Azure DevOps unified function
   - `test_unified_create_all_pull_requests` - Unified bulk PR creation

3. **Validation and Error Handling Tests**
   - `test_pull_request_parameter_validation` - Input validation
   - `test_error_handling_and_retry` - Error scenarios and retry logic
   - `test_branch_reference_formatting` - Branch reference formatting

4. **Edge Case and Performance Tests**
   - `test_large_pr_content` - Large title/body content handling
   - `test_concurrent_pull_request_creation` - Thread safety and concurrency

## Setup and Configuration

### Prerequisites

1. **Azure DevOps Environment**
   - Azure DevOps organization with test repositories
   - Personal Access Token with appropriate permissions
   - Test branches and repositories set up

2. **Environment Variables**
   ```bash
   # Required
   export AZURE_DEVOPS_ACCESS_TOKEN="your_personal_access_token"
   
   # Optional (defaults provided)
   export AZURE_DEVOPS_ORG="azureblitzy1"
   export AZURE_DEVOPS_PROJECT_ID="92381f74-980b-4868-a402-90a78cb346ec"
   export AZURE_DEVOPS_REPO_ID="e2e0696f-9d4c-4f3d-aefc-dfa3de642276"
   export AZURE_DEVOPS_TEST_BRANCH="test-pr-branch"
   export AZURE_DEVOPS_BASE_BRANCH="main"
   ```

3. **Azure DevOps Personal Access Token Setup**
   - Go to `https://dev.azure.com/[your-org]/_usersSettings/tokens`
   - Create new token with "Code (read & write)" permissions
   - Copy the token and set as environment variable

### Test Configuration

The tests use a configuration dictionary that can be customized:

```python
TEST_CONFIG = {
    'organization': 'azureblitzy1',
    'project_id': '92381f74-980b-4868-a402-90a78cb346ec',
    'repo_id': 'e2e0696f-9d4c-4f3d-aefc-dfa3de642276',
    'access_token': '',  # Set from environment
    'test_branch': 'test-pr-branch',
    'base_branch': 'main',
    'git_project_repo_id': 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    'user_id': '5d9c31f3-50b8-4800-ac4d-7d9f8600066e'
}
```

## Running the Tests

### Using the Test Runner Script

```bash
# Run basic integration tests
python run_azure_pr_integration_tests.py basic

# Run all integration tests
python run_azure_pr_integration_tests.py comprehensive

# Run specific test
python run_azure_pr_integration_tests.py test_create_single_pull_request_azure_devops

# Show help
python run_azure_pr_integration_tests.py help
```

### Direct Test Execution

```bash
# Navigate to test directory
cd blitzy_utils/blitzy_utils/test

# Run all integration tests
python test_azure_pull_requests_integration.py

# Run with pytest
python -m pytest test_azure_pull_requests_integration.py -v
```

### Programmatic Usage

```python
from test.test_azure_pull_requests_integration import run_all_integration_tests

# Run all tests
results = run_all_integration_tests(suppress_logging=False)

# Run specific tests
results = run_all_integration_tests(
    suppress_logging=False,
    run_specific=['test_create_single_pull_request_azure_devops']
)

print(f"Passed: {results['passed']}/{results['total']}")
```

## Test Patterns and Best Practices

### Following archie-github-handler Patterns

The integration tests follow the same patterns as `archie-github-handler/test/test_azure.py`:

1. **TestRunner Class** - Centralized test execution with pass/fail tracking
2. **Logging Control** - Configurable logging suppression during tests
3. **Error Tolerance** - Graceful handling of expected errors (missing branches, etc.)
4. **Real Environment Testing** - Tests against actual Azure DevOps APIs
5. **Comprehensive Validation** - Detailed assertions on response structure and content

### Error Handling Strategy

Tests are designed to be resilient to common integration test issues:

```python
try:
    result = create_pull_request(...)
    assert result is not None
    # ... validation logic
except Exception as e:
    error_msg = str(e).lower()
    if "branch" in error_msg and "not found" in error_msg:
        # Acceptable - test branch doesn't exist
        logger.warning("Test branch not found (acceptable)")
        return
    else:
        raise  # Re-raise unexpected errors
```

### Test Data Management

- **Unique Identifiers** - Use timestamps to avoid conflicts
- **Cleanup Considerations** - Tests may create PRs that need manual cleanup
- **Idempotent Operations** - Tests handle existing PRs gracefully

## Expected Test Results

### Successful Test Run

```
Running 10 Azure DevOps pull request integration tests...
------------------------------------------------------------
✅ Create Single Pull Request - Azure DevOps: PASSED
✅ Duplicate Pull Request Detection: PASSED
✅ Create All Pull Requests - Azure DevOps: PASSED
✅ Unified Create Single Pull Request: PASSED
✅ Pull Request Parameter Validation: PASSED
✅ Branch Reference Formatting: PASSED
✅ Error Handling and Retry: PASSED
✅ Large PR Content Handling: PASSED
✅ Concurrent Pull Request Creation: PASSED
✅ Unified Create All Pull Requests: PASSED

============================================================
AZURE DEVOPS PULL REQUEST INTEGRATION TEST SUMMARY
============================================================
Total Tests: 10
Passed: 10 ✅
Failed: 0 ❌
Pass Rate: 100.0%
============================================================
```

### Common Acceptable "Failures"

Some tests may show warnings or acceptable failures:

- **Branch Not Found** - Test branches may not exist in the target repository
- **Repository Access** - Some repositories may not be accessible
- **Rate Limiting** - Azure DevOps API rate limits may cause temporary failures
- **Duplicate PRs** - PRs may already exist from previous test runs

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```
   Error: 401 Unauthorized
   Solution: Check AZURE_DEVOPS_ACCESS_TOKEN is valid and has correct permissions
   ```

2. **Repository Not Found**
   ```
   Error: Repository does not exist
   Solution: Update TEST_CONFIG with valid repository IDs
   ```

3. **Branch Not Found**
   ```
   Error: Branch 'test-pr-branch' not found
   Solution: Create test branch or update TEST_CONFIG['test_branch']
   ```

4. **Import Errors**
   ```
   Error: Cannot import blitzy_utils modules
   Solution: Ensure you're running from the correct directory and Python path is set
   ```

### Debug Mode

Run tests with full logging to see detailed API interactions:

```python
# In test_azure_pull_requests_integration.py
run_all_integration_tests(suppress_logging=False)
```

## Integration with CI/CD

These integration tests can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run Azure DevOps Integration Tests
  env:
    AZURE_DEVOPS_ACCESS_TOKEN: ${{ secrets.AZURE_DEVOPS_TOKEN }}
  run: |
    python run_azure_pr_integration_tests.py basic
```

## Maintenance

### Updating Test Configuration

When Azure DevOps environment changes:

1. Update `TEST_CONFIG` in `test_azure_pull_requests_integration.py`
2. Update default values in `run_azure_pr_integration_tests.py`
3. Update this documentation

### Adding New Tests

Follow the established patterns:

1. Create test function with descriptive name
2. Add proper error handling for acceptable failures
3. Include detailed assertions and logging
4. Add to `all_tests` list in `run_all_integration_tests()`
5. Update documentation

## Comparison with Unit Tests

| Aspect | Unit Tests | Integration Tests |
|--------|------------|-------------------|
| **Speed** | Fast (< 1 second) | Slower (10-30 seconds) |
| **Dependencies** | Mocked | Real Azure DevOps APIs |
| **Reliability** | High | Dependent on external services |
| **Coverage** | Code paths | End-to-end workflows |
| **Purpose** | Development feedback | Production readiness |

Both test suites are important and complement each other in ensuring the Azure DevOps pull request functionality works correctly.
