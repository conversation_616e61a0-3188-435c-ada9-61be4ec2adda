[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "deployment-utils"
version = "0.0.212"
description = "Deployment util to handle various deployment related tasks."
readme = "README.md"
authors = [
    { name = "Chaitanya Baraskar" }
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: Apache 2.0 License",
    "Operating System :: OS Independent",
]
dependencies = [
    "pyyaml==6.0.2"
]
requires-python = ">=3.12"

[project.license]
text = "Apache 2.0"

[project.urls]
Homepage = "https://blitzy.ai"
Repository = "https://github.com/blitzy-ai/python-utils"

[project.scripts]
deploy-to-cloud-run = "deployment_utils.deploy_to_cloud_run:main"
generate-pydantic-models = "deployment_utils.generate_models:main"